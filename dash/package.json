{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build -p", "dev": "nuxt dev", "start": "nuxt start", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "tailwind-view": "tailwind-config-viewer -o"}, "devDependencies": {"@averjs/nuxt-compression": "^1.2.2", "@nuxt/devtools": "^0.5.5", "@nuxt/image": "latest", "@nuxtjs/color-mode": "^3.2.0", "@nuxtjs/eslint-module": "^4.1.0", "@nuxtjs/tailwindcss": "^6.13.1", "@pinia-plugin-persistedstate/nuxt": "^1.1.1", "@pinia/nuxt": "latest", "@sidebase/nuxt-auth": "^0.5", "@sidebase/nuxt-pdf": "^0.1.1", "@types/node": "^20", "@typescript-eslint/eslint-plugin": "^7", "@typescript-eslint/parser": "^7", "@vite-pwa/nuxt": "^1", "@vitejs/plugin-vue": "^5.0.0", "daisyui": "^4", "eslint": "^8.5", "eslint-config-prettier": "^8.8.0", "eslint-plugin-vue": "^9.19", "next-auth": "^4.22.1", "nuxt": "^3", "prettier": "^3", "sass": "^1.62.1", "tailwind-config-viewer": "^1.7.2", "typescript": "^5", "vite-plugin-checker": "^0.6.0", "vue-tsc": "^2", "yup": "^1.6"}, "dependencies": {"@heroicons/vue": "^2.0.18", "@preact/signals": "2", "@pusher/push-notifications-web": "^1.1.0", "@schedule-x/calendar": "^2.7.0", "@schedule-x/calendar-controls": "^2.7.0", "@schedule-x/current-time": "^2.7.0", "@schedule-x/event-modal": "^2.7.0", "@schedule-x/events-service": "^2.7.0", "@schedule-x/theme-default": "^2.7.0", "@schedule-x/vue": "^2.6.0", "@vee-validate/i18n": "^4.10.3", "@vee-validate/nuxt": "^4.10.3", "@vee-validate/yup": "^4.10.3", "@vue-hero-icons/solid": "^1.7.2", "axios": "^1.3.6", "chart.js": "^4.4.1", "colorjs.io": "^0.5.2", "dayjs": "^1.11.7", "laravel-echo": "^2.0.2", "maska": "^2.1.9", "nuxt-socket-io": "^3.0.12", "pinia": "^2.2.4", "pusher-js": "^8.4.0", "qalendar": "^3.7.0", "socket.io-client": "^4.8.1", "theme-change": "^2.5.0", "vee-validate": "^4.10.3", "vue": "^3.4", "vue-chartjs": "^5.3.0", "vue-currency-input": "^3.0.5", "vue-toast-notification": "^3.1.1", "vue3-dropzone": "^2.2.1"}}