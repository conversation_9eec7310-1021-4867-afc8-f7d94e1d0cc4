<template>
  <div class="bg-base-200">
    <NuxtPwaManifest />
    <NuxtLayout :name="layout">
      <NuxtPage :class="{
        'w-full h-full my-auto flex items-center': layout === 'default',
      }" />
    </NuxtLayout>
  </div>
</template>
<script setup lang="ts">
import "vue-toast-notification/dist/theme-bootstrap.css";

const route = useRoute();
const layout = computed(() => {
  if (
    [
      "/login",
      "/forgot-password",
      "/recover-password",
      "/recover-password/",
      "/criar-conta",
      "/completar-cadastro",
    ].includes(route.path)
  )
    return "unauthenticated";
  else return "default";
});
const getTitle = computed(() => {
  const routeName = useRoute().name;
  if (routeName === "servicos") return "Serviços";
  if (routeName === "agenda") return "Agenda";
  if (routeName === "profissionais") return "Profissionais";
  if (routeName === "clients") return "Pacientes";
  if (routeName === "movimentacao-produtos") return "Movimentações de produtos";
  if (routeName === "sales") return "Vendas";
  if (routeName === "movimentacoes") return "Movimentações";
  if (routeName === "relatorios") return "Relatórios";
  if (routeName === "config") return "Meu negócio";
  if (routeName === "produtos") return "Produtos";
  if (routeName === "relatorio") return "Relatório";
  if (routeName === "contas") return "Contas a pagar";
  if (routeName === "cores") return "Estilização";
  if (routeName === "clientes") return "Meus Pacientes";
  if (routeName === "orders") return "Agendamentos";
  if (routeName === "horarios") return "Horários";
  if (routeName === "anotacoes") return "Anotações";
  return "Psy +";
});

useHead({
  link: [
    {
      rel: "icon",
      type: "image/x-icon",
      href: `${useRuntimeConfig().public.API_URL}/icons/logo.png`,
    },
    // Temporarily disable manifest to prevent service worker registration
    // {
    //   rel: "manifest",
    //   href: "/manifest.json",
    // },
  ],
  title: getTitle.value,
  htmlAttrs: {
    lang: "pt-BR",
  },
});
useSeoMeta({
  description:
    "Sistema de agendamento e gestão para psicologos",
  ogTitle: "Psy +",
  ogSiteName: "Psy +",
  ogDescription:
    "Sistema de agendamento e gestão para psicologos",
  ogImage: `${useRuntimeConfig().public.API_URL}/icons/logo.png`,
});
</script>
