/** @type {import('tailwindcss').Config} */
module.exports = {
  plugins: [require("daisyui")],
  daisyui: {
    themes: [
      {
        light: {
          ...require("daisyui/src/theming/themes")["light"],
          "primary-content": "#FFF",
          primary: "#4B87A4",
        },
      },
      {
        dark: {
          ...require("daisyui/src/theming/themes")["dark"],
          "primary-content": "#000",
          primary: "#4B87A4",
        },
      },
      // {
      //   mytheme: {
      //     "primary": "#2d7c85",
      //     "secondary": "#86198f",
      //     "accent": "#881337",
      //     "neutral": "#191D24",
      //     // "base-100": "#374151",
      //     "base-content": "#E4E4E4",
      //     "base-100": "#2A303C",
      //     "info": "#4f46e5",
      //     "success": "#86efac",
      //     "warning": "#facc15",
      //     "error": "#b91c1c",
      //   },
      // },
    ],
  },
};
