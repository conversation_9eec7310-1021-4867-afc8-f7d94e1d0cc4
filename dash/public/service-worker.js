// Pusher <PERSON>ams integration for service worker
importScripts("https://js.pusher.com/beams/service-worker.js");
import { precacheAndRoute } from 'workbox-precaching'

precacheAndRoute(self.__WB_MANIFEST)
self.addEventListener("push", (event) => {
  const payload = event.data ? event.data.json() : {};
  event.waitUntil(
    self.clients.matchAll().then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: "notification",
          data: payload.notification, // Passa os dados da notificação para a aplicação
        });
      });
    })
  );
});