<template>
  <div class="container-table flex-col md:flex-row">
    <!-- <div
      class="hidden md:block bg-gray-500 w-[2px] divider divider-horizontal mx-5 my-auto h-full"
    ></div> -->
  </div>
</template>
<script setup lang="ts">
// function changeTheme(theme: string) {
//   document.documentElement.classList.remove(
//     "background",
//     "dark",
//     "background-2"
//   );
//   document.documentElement.classList.add(theme);

//   document.documentElement.dataset.theme = theme;
// }

// // import { useLoginStore } from "@/store/user";
// import { ColorPicker } from "vue3-colorpicker";
// import "vue3-colorpicker/style.css";

// const isOpened = ref(false);

// defineComponent({
//   components: {
//     ColorPicker,
//   },
// });

// const audio = ref(new Audio("/songs/notify.mp3"));
// // const userStore = useLoginStore();
// const currentTime = ref(0);
// const duration = ref(0);
// function selectedFile(file: File) {
//   console.log(file);
//   isOpened.value = false;
// }

// function handleFile(event: Event) {
//   const fileInput = event.target as HTMLInputElement;
//   const file = fileInput.files?.[0];
//   if (!file) return;
//   const url = URL.createObjectURL(file as Blob);
//   const newAudio = new Audio(url);
//   newAudio.setAttribute("name", file.name || "notify.mp3");
//   audio.value.onplaying;
//   audio.value = newAudio;
// }

// watch(
//   audio,
//   () => {
//     audio.value.addEventListener("loadedmetadata", () => {
//       duration.value = parseInt(audio.value.duration.toString());
//       currentTime.value = 0;
//     });
//     audio.value.ontimeupdate = () => {
//       currentTime.value = parseInt(audio.value.currentTime.toString());
//     };
//   },
//   { immediate: true }
// );
</script>
