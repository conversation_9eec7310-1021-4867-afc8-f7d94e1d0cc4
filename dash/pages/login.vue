<template>
  <div class="bg-base-200 flex items-center h-full">
    <div class="grid md:grid-cols-9 w-full h-full grid-cols-1 lg:grid-cols-2 bg-base-100 rounded-xl">
      <ClientOnly>
        <div class="pb-24 px-8 mx-auto my-auto md:my w-full max-w-lg col-span-1 md:col-span-4 lg:col-span-1">
          <div class="text-xl md:text-2xl mb-2 md:mb-4 font-semibold text-center pt-12">
            Psy + para profissionais
          </div>
          <div class="font-semibold mb-3">
            Faça login ou
            <nuxt-link to="/criar-conta" class="text-primary underline">crie uma nova conta</nuxt-link>
            e teste grátis por 30 dias
          </div>
          <div class="text-red-500 text-center" v-if="error">
            {{ error }}
          </div>
          <login-form @submit="handleFormSubmit" :loading="loading" />
        </div>
        <div class="hidden md:inline-block hero min-h-full md:col-span-5 lg:col-span-1 rounded-l-xl">
          <nuxt-img loading="lazy" :src="`https://psyplus.site/assets/login-image.jpg`"
            alt="Dashwind Admin Template" class="h-full"></nuxt-img>
        </div>
      </ClientOnly>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "@/server/api";
import { useLoginStore } from "@/store/user";
const loading = ref(false);
const loginStore = useLoginStore();
const router = useRouter();
const runtimeConfig = useRuntimeConfig();
const error = ref();
async function handleFormSubmit(payload: { email: string; password: string }) {
  try {
    error.value = undefined;
    loading.value = true;

    console.log("Login attempt with:", payload);

    // Call the backend API for authentication
    const { data } = await api.post("/auth/login", payload);

    // Store the token with expiry
    loginStore.login(data.token, data.expires_at);

    // Get user data from the /auth/me endpoint
    try {
      const userResponse = await api.get("/auth/me", {
        headers: {
          Authorization: `Bearer ${data.token}`
        }
      });

      // Set user data from response
      const userData = {
        email: userResponse.data.email,
        image: userResponse.data.image || "",
        name: userResponse.data.fullName || "",
        phone: userResponse.data.phone || "",
      };

      loginStore.setUser(userData);
    } catch (userError) {
      console.error("Error fetching user data:", userError);

      // If we can't get user data, at least set the email
      loginStore.setUser({
        email: payload.email,
        image: "",
        name: "",
        phone: "",
      });
    }

    router.push("/");
  } catch (err) {
    console.error("Login error:", err);
    error.value = "Email ou senha incorretos";
  } finally {
    loading.value = false;
  }
}
</script>
