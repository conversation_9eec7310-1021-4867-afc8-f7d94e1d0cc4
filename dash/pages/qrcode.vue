<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base hide-edit hide-delete :total-items="totalItems" v-model:per_page="perPage" v-model:page="page"
          title="Adicionar número" :columns="columns" :rows="whatsappNumbers" class="flex-1" @new="isOpened = true"
          :loading="loading" :actions="[
            { name: 'Excluir', action: deleteWhatsAppNumber }
          ]">
          <template #filter>
            <div class="flex gap-4 items-center">
              <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
                v-model="filters.projectName" />
              <input-base class="hidden sm:flex" :options="[
                { value: 'project_name', label: 'Nome do Projeto' },
                { value: 'phone_number', label: 'Número' },
              ]" label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sortBy" />
              <input-base class="hidden sm:flex" :options="[
                { value: 'asc', label: 'Crescente' },
                { value: 'desc', label: 'Decrescente' },
              ]" label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button @click="filterModalOpen = true" size="sm" class="mr-3 btn-circle">
                  <AdjustmentsHorizontalIcon class="w-5 " />
                </base-button>
              </div>
            </div>
          </template>
          <template #projectName="{ row }">
            <td>
              {{ row.projectName }}
            </td>
          </template>
          <template #phoneNumber="{ row }">
            <td>
              {{ formatPhoneNumber(row.phoneNumber) }}
            </td>
          </template>
          <template #status="{ row }">
            <td>
              <span
                class="badge"
                :class="{
                  'badge-warning': row.status === 'pending',
                  'badge-success': row.status === 'active',
                  'badge-error': row.status === 'inactive'
                }"
              >
                {{ translateStatus(row.status) }}
              </span>
            </td>
          </template>
        </table-base>
      </div>
    </div>

    <!-- QR Code Modal for New Connection -->
    <base-dialog title="Conectar Novo Número de WhatsApp" v-model="isOpened" @close="closeNewQRCodeModal">
      <div class="flex flex-col items-center justify-center p-4">
        <div v-if="loading" class="flex items-center justify-center h-64 w-64">
          <span class="loading loading-spinner loading-lg"></span>
        </div>
        <div v-else-if="newQRCodeUrl" class="bg-white p-4 rounded-lg shadow-md mb-4">
          <img :src="newQRCodeUrl" alt="QR Code" class="h-64 w-64" />
        </div>
        <div v-else class="flex items-center justify-center h-64 w-64 border border-dashed border-gray-300 rounded-lg">
          <p class="text-gray-500">Clique em "Gerar QR Code" para começar</p>
        </div>

        <p class="text-center text-sm mt-4 mb-4">
          Abra o WhatsApp no seu celular, vá em <strong>Menu > Aparelhos conectados > Conectar um aparelho</strong> e escaneie o QR code para vincular um novo número.
        </p>

        <div class="text-center text-xs text-gray-500 mb-4">
          <p>Este QR code é gerado pelo WhatsApp e expira após 60 segundos.</p>
          <p>Se expirar, clique em "Gerar Novo QR Code".</p>
          <p>Após escanear, você será solicitado a fornecer um nome para o projeto.</p>
        </div>

        <div class="w-full space-y-3">
          <base-button @click="generateNewQRCode" class="w-full" :loading="generatingNewQR">
            {{ newQRCodeUrl ? 'Gerar Novo QR Code' : 'Gerar QR Code' }}
          </base-button>

          <base-button v-if="newQRCodeUrl" @click="simulateNewQRScan" class="w-full" color="success">
            Simular Escaneamento (Apenas para teste)
          </base-button>
        </div>
      </div>
    </base-dialog>

    <!-- QR Code Modal -->
    <base-dialog title="Escaneie o QR Code" v-model="qrCodeModalOpen" @close="closeQRCodeModal">
      <div class="flex flex-col items-center justify-center p-4">
        <div v-if="currentQRCode" class="bg-white p-4 rounded-lg shadow-md mb-4">
          <img :src="currentQRCode.qrCodeUrl" alt="QR Code" class="h-64 w-64" />
        </div>
        <p class="text-center text-sm mt-2 mb-4">
          Abra o WhatsApp no seu celular, vá em <strong>Menu > Aparelhos conectados > Conectar um aparelho</strong> e escaneie o QR code para vincular o número.
          <br>
          <span class="font-semibold">Projeto:</span> {{ selectedNumber?.projectName }}
          <br>
          <span class="font-semibold">Número:</span> {{ selectedNumber ? formatPhoneNumber(selectedNumber.phoneNumber) : '' }}
        </p>
        <div class="text-center text-xs text-gray-500 mb-4">
          <p>Este QR code é gerado pelo WhatsApp e expira após 60 segundos.</p>
          <p>Se expirar, feche este modal e gere um novo QR code.</p>
        </div>
        <base-button @click="simulateQRScan" class="w-full mt-2" color="success">
          Simular Escaneamento (Apenas para teste)
        </base-button>
      </div>
    </base-dialog>

    <!-- Filter Modal -->
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
          v-model="filters.projectName" />
        <input-base :options="[
          { value: 'project_name', label: 'Nome do Projeto' },
          { value: 'phone_number', label: 'Número' },
        ]" label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sortBy" />
        <input-base :options="[
          { value: 'asc', label: 'Crescente' },
          { value: 'desc', label: 'Decrescente' },
        ]" label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" />
        <base-button @click="handleFilter" class="w-full mt-4">
          Aplicar Filtros
        </base-button>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";
import { ref, onMounted, watch } from "vue";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useLoginStore } from "~/store/user";

const toast = useToast();
const loginStore = useLoginStore();

// State variables
const whatsappNumbers = ref<Array<{
  id: number;
  phoneNumber: string;
  contactName: string;
  projectName: string;
  status: string;
  qrCodeId?: string;
  createdAt: string;
  updatedAt: string;
}>>([]);
const loading = ref(false);
const generatingQR = ref<number | null>(null);
const generatingNewQR = ref(false);
const currentQRCode = ref<{
  id: number;
  qrCodeId: string;
  qrCodeUrl: string;
  phoneNumber: string;
  contactName: string;
  projectName: string;
  status: string;
  createdAt: string;
} | null>(null);
const selectedNumber = ref<typeof whatsappNumbers.value[0] | null>(null);
const newQRCodeUrl = ref<string | null>(null);
const selectedTempNumberId = ref<number | null>(null);
const totalItems = ref(0);
const page = ref(1);
const perPage = ref(10);
const isOpened = ref(false);
const qrCodeModalOpen = ref(false);
const filterModalOpen = ref(false);

// New number form
const newNumber = ref({
  contactName: '',
  phoneNumber: '',
  projectName: '',
});

// Filters
const filters = ref({
  projectName: "",
  phoneNumber: "",
  sortBy: "project_name",
  direction: "asc",
});

// Table columns
const columns = [
  { label: "Nome do projeto", key: "projectName", sm: true },
  { label: "Número vinculado", key: "phoneNumber" },
  { label: "Status", key: "status" },
];

// Reset form values
function resetFormValues() {
  newNumber.value = {
    contactName: '',
    phoneNumber: '',
    projectName: '',
  };
}

// Handle filter button click
function handleFilter() {
  filterModalOpen.value = false;
  getData();
}

// Fetch WhatsApp numbers
async function getData() {
  try {
    loading.value = true;

    const token = loginStore.token;
    if (!token) {
      toast.error('Você precisa estar logado para visualizar os números.');
      return;
    }

    const { data } = await api.get('/whatsapp', {
      params: {
        projectName: filters.value.projectName,
        phoneNumber: filters.value.phoneNumber,
        sortBy: filters.value.sortBy,
        direction: filters.value.direction,
        page: page.value,
        perPage: perPage.value
      },
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // Log the response to see what we're getting
    console.log('WhatsApp numbers response:', data);

    // Map the data to match the expected format

    if (data && Array.isArray(data)) {
      whatsappNumbers.value = data.map((item: any) => ({
        id: item.id,
        phoneNumber: item.phoneNumber || '',
        contactName: item.contactName || '',
        projectName: item.projectName || '',
        status: item.status || 'pending',
        qrCodeId: item.qrCodeId,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
    } else {
      whatsappNumbers.value = [];
    }

    totalItems.value = data.total || 0;

    console.log('Processed WhatsApp numbers:', whatsappNumbers.value);
  } catch (error) {
    console.error('Error fetching WhatsApp numbers:', error);
    toast.error('Erro ao carregar números de WhatsApp. Tente novamente.');
  } finally {
    loading.value = false;
  }
}

// Generate new QR code for connecting a new WhatsApp number
async function generateNewQRCode() {
  try {
    generatingNewQR.value = true;

    const token = loginStore.token;
    if (!token) {
      toast.error('Você precisa estar logado para gerar um QR code.');
      return;
    }

    // Create a temporary WhatsApp number to generate a QR code
    const response = await api.post('/whatsapp', {
      phoneNumber: '0000000000', // Temporary number
      contactName: 'Novo Número',
      projectName: 'Novo Projeto',
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // Generate QR code for the temporary number
    const qrResponse = await api.post(`/whatsapp/${response.data.id}/generate`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    newQRCodeUrl.value = qrResponse.data.qrCodeUrl;

    // Store the temporary number ID for later reference
    selectedTempNumberId.value = response.data.id;

    toast.success('QR Code gerado com sucesso! Escaneie com o WhatsApp para conectar.');
  } catch (error) {
    console.error('Error generating QR code:', error);
    toast.error('Erro ao gerar QR code. Tente novamente.');
  } finally {
    generatingNewQR.value = false;
  }
}

// Simulate scanning the new QR code
async function simulateNewQRScan() {
  try {
    const token = loginStore.token;
    if (!token) {
      toast.error('Você precisa estar logado para conectar um número.');
      return;
    }

    if (!selectedTempNumberId.value) {
      toast.error('Nenhum número temporário encontrado. Gere um novo QR code.');
      return;
    }

    toast.info('Em um cenário real, você escanearia o QR code com o WhatsApp. Esta é apenas uma simulação.');

    // In a real implementation, the WhatsApp client would connect automatically
    // after scanning the QR code. Here we're just simulating the process.

    // Update the temporary WhatsApp number with real information
    await api.put(`/whatsapp/${selectedTempNumberId.value}`, {
      phoneNumber: '5511999999999', // Simulated phone number
      contactName: 'Número Simulado',
      projectName: 'Projeto Teste',
      status: 'active'
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // Simulate activation
    await api.post(`/whatsapp/${selectedTempNumberId.value}/activate`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    toast.success('Número conectado com sucesso!');
    closeNewQRCodeModal();

    // Reset the temporary number ID
    selectedTempNumberId.value = null;

    // Refresh the list
    getData();
  } catch (error) {
    console.error('Error connecting WhatsApp number:', error);
    toast.error('Erro ao conectar número. Tente novamente.');
  }
}
async function deleteWhatsAppNumber(number: any) {
  try {
    const token = loginStore.token;
    if (!token) {
      toast.error('Você precisa estar logado para excluir um número.');
      return;
    }

    await api.delete(`/whatsapp/${number.id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    toast.success('Número excluído com sucesso!');
    getData();
  } catch (error) {
    console.error('Error deleting WhatsApp number:', error);
    toast.error('Erro ao excluir número. Tente novamente.');
  }
}

// Close new QR code modal
function closeNewQRCodeModal() {
  isOpened.value = false;
  newQRCodeUrl.value = null;
  selectedTempNumberId.value = null;
}

// Generate QR code for a WhatsApp number
async function generateQRCode(number: any) {
  try {
    generatingQR.value = number.id;

    const token = loginStore.token;
    if (!token) {
      toast.error('Você precisa estar logado para gerar um QR code.');
      return;
    }

    const response = await api.post(`/whatsapp/${number.id}/generate`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    currentQRCode.value = response.data;
    selectedNumber.value = number;

    // Open QR code modal
    qrCodeModalOpen.value = true;
  } catch (error) {
    console.error('Error generating QR code:', error);
    toast.error('Erro ao gerar QR code. Tente novamente.');
  } finally {
    generatingQR.value = null;
  }
}

// Simulate QR code scan (for testing)
async function simulateQRScan() {
  if (!selectedNumber.value) return;

  try {
    const token = loginStore.token;
    if (!token) {
      toast.error('Você precisa estar logado para ativar um número.');
      return;
    }

    await api.post(`/whatsapp/${selectedNumber.value.id}/activate`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    toast.success('Número ativado com sucesso!');
    closeQRCodeModal();

    // Refresh the list
    await getData();
  } catch (error) {
    console.error('Error activating WhatsApp number:', error);
    toast.error('Erro ao ativar número. Tente novamente.');
  }
}

// Close QR code modal
function closeQRCodeModal() {
  qrCodeModalOpen.value = false;
  currentQRCode.value = null;
  selectedNumber.value = null;

  // Refresh the list to show updated status
  getData();
}

// Helper functions
function formatPhoneNumber(phone: string) {
  if (!phone) return '';

  // Format as (XX) XXXXX-XXXX
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
  }
  return phone;
}

function translateStatus(status: string) {
  const statusMap: Record<string, string> = {
    'pending': 'Pendente',
    'active': 'Ativo',
    'inactive': 'Inativo',
  };

  return statusMap[status] || status;
}

// Debounced fetch for filters
function debouncedFetch() {
  setTimeout(() => getData(), 500);
}

// Watch for filter changes
watch(
  filters,
  () => {
    if (filterModalOpen.value) return;
    if (filters.value.projectName) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

// Watch for page changes
watch(
  [page, perPage],
  () => {
    getData();
  }
);

// Watch for modal close
watch(isOpened, () => {
  if (!isOpened.value) {
    resetFormValues();
  }
});

// Load WhatsApp numbers on component mount
onMounted(() => {
  getData();
});
</script>
