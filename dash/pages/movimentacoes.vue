<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base
          :total-items="totalItems"
          title="Serviços"
          :columns="columns"
          :rows="services"
          class="flex-1"
          @new="handleNew"
          @edit="handleEdit"
          @delete="deleteServiceById"
          hide-edit
          hide-delete
          :loading="loading"
          v-model:per_page="per_page"
          v-model:page="page"
          no-create
          :actions="[{ name: 'Visualizar', action: handleViewMovement }]"
        >
          <template #filter>
            <div class="flex gap-4 items-center">
              <input-base
                :options="[
                  { value: 'product', label: 'Produto' },
                  { value: 'service', label: 'Serviço' },
                  { value: 'bill', label: 'Conta' },
                  { value: '', label: 'Todos' },
                ]"
                label="Categoria"
                v-model="filters.category"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                v-if="filters.category === 'product'"
                :options="[
                  { value: 'purchase', label: 'Co<PERSON>ra' },
                  { value: 'sale', label: 'Venda' },
                  { value: 'waste', label: 'Avaria' },
                  { value: 'other', label: 'Outro' },
                ]"
                label="Motivo"
                v-model="filters.value"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                :options="[
                  { value: 'quantity', label: 'Qtd movimentada' },
                  { value: 'total_price', label: 'Preço Total' },
                  { value: 'created_at', label: 'Data' },
                ]"
                class="hidden sm:flex"
                label="Ordenar por"
                v-model="filters.sortBy"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                :options="[
                  { value: 'asc', label: 'Crescente' },
                  { value: 'desc', label: 'Decrescente' },
                ]"
                class="hidden sm:flex"
                label="Ordem"
                v-model="filters.sortDirection"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button
                  @click="filterModalOpen = true"
                  size="sm"
                  class="mr-3 btn-circle"
                >
                  <AdjustmentsHorizontalIcon class="w-5" />
                </base-button>
              </div>
            </div>
          </template>

          <template #name="{ value }">
            <td class="text-xs sm:text-base">
              {{ value }}
              <!-- <span class="">
                {{ row.product_variant?.name }}
              </span> -->
            </td>
          </template>
          <template #type="{ value }">
            <td
              class="w-10 md:w-16 text-xs sm:text-base hidden sm:table-cell"
              :class="value === 'profit' ? '' : ''"
            >
              <!-- <PlusCircleIcon
                v-if="value === 'profit'"
                class="w-6 h-6 text-green-600"
              />
              <MinusCircleIcon v-else class="w-6 h-6 text-red-600" /> -->
              {{ value === "profit" ? "Lucro" : "Gasto" }}
            </td>
          </template>
          <template #reason="{ value }">
            <td class="md:table-cell hidden text-xs sm:text-sm w-20">
              {{ getReason(value) }}
            </td>
          </template>
          <template #quantity="{ value }">
            <td class="text-center w-4 md:w-14 text-sm sm:text-base">
              {{ value }}
            </td>
          </template>
          <template #created_at="{ value }">
            <td
              class="md:table-cell hidden text-center md:w-20 text-sm sm:text-base"
            >
              {{ dayjs(value).format("DD/MM/YYYY") }}
            </td>
          </template>
        </table-base>
      </div>
    </div>

    <!-- Dialog for adding/editing products -->
    <base-dialog title="Adicionar Produto" v-model="isOpened">
      <ProductsMovementRegisterForm
        v-if="isOpened"
        @submit="handleFormSubmit"
        :product="selectedService"
      />
    </base-dialog>

    <!-- Dialog for filters -->
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base
          :options="[
            { value: 'product', label: 'Produto' },
            { value: 'service', label: 'Serviço' },
            { value: 'bill', label: 'Conta' },
            { value: '', label: 'Todos' },
          ]"
          label="Categoria"
          v-model="filters.category"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />
        <input-base
          v-if="filters.category === 'product'"
          :options="[
            { value: 'purchase', label: 'Compra' },
            { value: 'sale', label: 'Venda' },
            { value: 'waste', label: 'Avaria' },
            { value: 'used', label: 'Usado' },
            { value: 'other', label: 'Outro' },
          ]"
          label="Motivo"
          v-model="filters.value"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <input-base
          :options="[
            { value: 'quantity', label: 'Quantidade' },
            { value: 'total_price', label: 'Preço Total' },
            { value: 'created_at', label: 'Data' },
          ]"
          label="Ordenar por"
          v-model="filters.sortBy"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <input-base
          :options="[
            { value: 'asc', label: 'Crescente' },
            { value: 'desc', label: 'Decrescente' },
          ]"
          label="Ordem"
          v-model="filters.sortDirection"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>

    <base-dialog title="Detalhes do Movimento" v-model="isMovementDialogOpen">
      <div v-if="selectedMovement" class="p-4">
        <div class="grid grid-cols-2 gap-4">
          <!-- Common fields for all types -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Nome</label>
            <p class="mt-1">{{ selectedMovement.name }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Tipo</label>
            <p class="mt-1">
              {{ selectedMovement.type === "profit" ? "Lucro" : "Gasto" }}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Total</label>
            <p class="mt-1">{{ toBrl(selectedMovement.total) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Data</label>
            <p class="mt-1">
              {{
                dayjs(selectedMovement.created_at).format("DD/MM/YYYY HH:mm")
              }}
            </p>
          </div>

          <template v-if="selectedMovement.category === 'product'">
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Produto</label
              >
              <p class="mt-1">{{ selectedMovement.product?.name }}</p>
            </div> -->
            <div v-if="selectedMovement.product_variant">
              <label class="block text-sm font-medium text-gray-700"
                >Variante</label
              >
              <p class="mt-1">{{ selectedMovement.product_variant?.name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Quantidade</label
              >
              <p class="mt-1">{{ selectedMovement.quantity }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Motivo</label
              >
              <p class="mt-1">{{ getReason(selectedMovement.reason) }}</p>
            </div>
          </template>

          <!-- Service specific fields -->
          <template v-if="selectedMovement.category === 'service'">
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Serviço</label
              >
              <p class="mt-1">{{ selectedMovement.service?.name }}</p>
            </div> -->
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Cliente</label
              >
              <p class="mt-1">{{ selectedMovement.client?.name }}</p>
            </div> -->
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Status</label
              >
              <p class="mt-1">{{ selectedMovement.status }}</p>
            </div> -->
          </template>

          <!-- Bill specific fields -->
          <template v-if="selectedMovement.category === 'bill'">
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Conta</label
              >
              <p class="mt-1">{{ selectedMovement.bill?.name }}</p>
            </div> -->
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Vencimento</label
              >
              <p class="mt-1">
                {{ dayjs(selectedMovement.due_date).format("DD/MM/YYYY") }}
              </p>
            </div> -->
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700"
                >Status</label
              >
              <p class="mt-1">{{ selectedMovement.status }}</p>
            </div> -->
          </template>

          <!-- Notes field for all types -->
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">Notas</label>
            <p class="mt-1">{{ selectedMovement.notes || "Nenhuma nota" }}</p>
          </div>
        </div>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import dayjs from "dayjs";
import { MinusCircleIcon, PlusCircleIcon } from "@heroicons/vue/24/outline";
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";

const filterModalOpen = ref(false);
const loading = ref(false);
const services = ref([]);
const selectedService = ref();
const isOpened = ref(false);
const isMovementDialogOpen = ref(false); // New ref for movement dialog
const selectedMovement = ref(); // New ref to store selected movement
const toast = useToast();
const per_page = ref(10);
const page = ref(1);
const totalItems = ref(0);

const filters = ref({
  category: "",
  value: "",
  sortBy: "created_at",
  sortDirection: "desc",
});

const columns = [
  { label: "Nome", key: "name", sm: true },
  { label: "Tipo", key: "reason" },
  { label: "Qtd", key: "quantity", sm: true },
  { label: "Data", key: "created_at", center: true },
  { label: "Total", key: "total", type: "currency" },
];

// Fetch data function
async function getData() {
  try {
    loading.value = true;
    const { data } = await api.get("/movements", {
      params: {
        category: filters.value.category,
        value: filters.value.value,
        filter_by: "reason",
        sort_by: filters.value.sortBy,
        direction: filters.value.sortDirection,
        per_page: per_page.value,
        page: page.value,
      },
    });
    services.value = data.data;
    totalItems.value = data.total;
  } catch (err) {
    console.error(err);
    toast.error("Erro ao buscar os dados!");
  } finally {
    loading.value = false;
  }
}

// Handle viewing full movement details
function handleViewMovement(row: any) {
  selectedMovement.value = row;
  isMovementDialogOpen.value = true;
}

// Existing functions remain unchanged
function handleFilter() {
  filterModalOpen.value = false;
  getData();
}

async function handleFormSubmit(product: any) {
  try {
    loading.value = true;
    if (selectedService.value) {
      await api.put(`/movements/${product.id}`, product);
      toast.success("Serviço atualizado com sucesso!");
    } else {
      await api.post(`/product-movements`, product);
      toast.success("Serviço criado com sucesso!");
    }
    selectedService.value = null;
    isOpened.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

const reasons = [
  { label: "Compra", value: "purchase" },
  { label: "Venda", value: "sale" },
  { label: "Avaria", value: "waste" },
  { label: "Uso", value: "used" },
  { label: "Outro", value: "other" },
  { label: "Serviço", value: null },
];

function getReason(value: string) {
  const [reason] = reasons.filter((reason) => reason.value === value);
  return reason?.label;
}

const debouncedFetch = () => useDelay(async () => await getData(), 500);

watch(
  filters,
  () => {
    if (filterModalOpen.value) return;
    if (filters.value.value) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

async function deleteServiceById(item: any) {
  try {
    if (item.id) {
      await api.delete(`/products/${item.id}`);
      toast.warning("Serviço deletado!");
    }
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  }
}

function handleEdit(product: any) {
  selectedService.value = product;
  isOpened.value = true;
}

function handleNew() {
  selectedService.value = undefined;
  isOpened.value = true;
}

onMounted(getData);

watch([page], () => {
  getData();
});
</script>
