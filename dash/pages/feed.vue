<template>
  <div class="container-table">
    <div class="flex justify-between">
      <div class="text-lg font-bold">Postagens ({{ posts.length }})</div>
      <base-button size="sm">
        <!-- <plus-icon class="w-5 h-5" /> -->
        Nova Postagem
      </base-button>
    </div>
    <feed-card
      v-for="({ text, user, image }, i) in posts"
      :key="i"
      :text="text"
      :user="user"
      :image="image"
    ></feed-card>
  </div>
</template>

<script setup lang="ts">
const posts = [
  {
    user: {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "senha123",
      image:
        "https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg",
      worker: true,
    },
    text: "lorem Ips et dolor sit amet, consectetur adipiscing elit",
    image: "/post-exemplo.jpg",
  },
  {
    user: {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "senha123",
      image:
        "https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg",
      worker: true,
    },
    text: "lorem Ips et dolor sit amet, consectetur adipiscing elit",
    image: "/post-exemplo.jpg",
  },

  {
    user: {
      id: "1",
      name: "João",
      email: "<EMAIL>",
      password: "senha123",
      image:
        "https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg",
      worker: true,
    },
    text: "lorem Ips et dolor sit amet, consectetur adipiscing elit",
    image: "/post-exemplo.jpg",
  },
];
</script>
