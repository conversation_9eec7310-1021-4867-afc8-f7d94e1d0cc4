<template>
    <div>
      <div class="container-table">
        <table-base
          title="Produtos"
          :columns="columns"
          :rows="cashs"
          @edit="handleEdit"
          @delete="deleteProduct"
          @new="isOpened = true"
        />
      </div>
      <base-dialog title="Adicionar Produto" v-model="isOpened">
        <products-register-form
          v-if="isOpened"
          @submit="handleFormSubmit"
          :product="selectedProduct"
        />
      </base-dialog>
      <loading :show="cashStore.loading" />
    </div>
  </template>
  <script setup lang="ts">
  import { useCashStore } from "@/store/cash";
import { useToast } from "vue-toast-notification";
  const isOpened = ref(false);
  const toast = useToast();
  const cashs = ref<Cash[]>([]);
  const cashStore = useCashStore();
  const selectedProduct = ref<Cash>();
  
  const columns = [
    { label: "Nome", key: "name" },
    { label: "Preço", key: "price" },
    // { label: "Quantidade", key: "quantity" },
  ];
  async function handleFormSubmit(product: Cash) {
    try {
      const newPrice = parseFloat(
        product.price
          .toString()
          .replaceAll(".", "")
          .replace(",", ".")
          .replace("R$", "")
      );
  
      const formatProduct = {
        ...product,
        price:
          product.price === selectedProduct.value?.price
            ? product.price
            : newPrice,
      };
  
      if (selectedProduct.value) {
        await cashStore.edit(formatProduct as Cash);
        toast.success("Produto atualizado com sucesso!");
        selectedProduct.value = undefined;
        isOpened.value = false;
        await getData();
        return;
      } else {
        await cashStore.add(formatProduct as Cash);
        toast.success("Produto criado com sucesso!");
      }
    } catch (err) {
      toast.error("Error request!");
    }
    isOpened.value = false;
    await getData();
  }
  
  async function deleteProduct(item: Cash) {
    try {
      await cashStore.delete(item.id);
      toast.warning("Produto deletado!");
      await getData();
    } catch (err) {
      console.error(err);
      toast.error("Error request!");
    }
  }
  
  async function getData() {
    try {
      const data = await cashStore.getAll();
      cashs.value = data;
    } catch (error) {
      console.error(error);
      toast.error("Error request!");
    }
  }
  function handleEdit(product: Cash) {
    selectedProduct.value = product;
    isOpened.value = true;
  }
  watch(isOpened, () => !isOpened && (selectedProduct.value = undefined));
  onMounted(async () => {
    await getData();
  });
  </script>
  