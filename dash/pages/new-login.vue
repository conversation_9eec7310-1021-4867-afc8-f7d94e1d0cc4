<template>
  <div class="min-h-screen bg-base-200 flex items-center">
    <div class="card mx-auto w-full max-w-2xl shadow-xl">
      <div class="grid  grid-cols-1 bg-base-100 rounded-xl">
        <div class="py-24 px-10">
          <h2 class="text-2xl font-semibold mb-2 text-center">Login</h2>
          <form @submit.prevent="submitForm">
            <div class="mb-4">
              <input-base
                type="emailId"
                v-model="loginForm.emailId"
                updateType="emailId"
                containerStyle="mt-4"
                labelTitle="Email Id"
              />

              <input-base
                v-model="loginForm.password"
                type="password"
                updateType="password"
                containerStyle="mt-4"
                labelTitle="Password"
              />
            </div>

            <div class="text-right text-primary">
              <nuxt-link to="/forgot-password">
                <span
                  class="text-sm inline-block hover:text-primary hover:underline hover:cursor-pointer transition duration-200"
                  >Forgot Password?</span
                >
              </nuxt-link>
            </div>

            <base-button
              type="submit"
              class="mt-2 w-full"
              :class="{ loading: loading }"
            >
              Login
            </base-button>

            <div class="text-center mt-4">
              Don't have an account yet?
              <nuxt-link to="/register">
                <span
                  class="inline-block hover:text-primary hover:underline hover:cursor-pointer transition duration-200"
                  >Register</span
                >
              </nuxt-link>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();
const INITIAL_LOGIN_OBJ = {
  password: "",
  emailId: "",
};

const loading = ref(false);
const loginForm = reactive(INITIAL_LOGIN_OBJ);

const submitForm = () => {
  loading.value = true;
  localStorage.setItem("token", "DumyTokenHere");
  loading.value = false;
  router.push("/app/welcome");
};
</script>
