<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base :total-items v-model:per_page="per_page" v-model:page="page" title="Cadastrar paciente"
          :columns="columns" :rows="services" class="flex-1" @new="isOpened = true" :loading="loading" :actions="[
            { name: 'Visualizar', action: handleViewUser }
          ]">
          <template #filter>
            <div class="flex gap-4 items-center">
              <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
                v-model="filters.name" />
              <!-- <input-base type="text" label="Filtrar por Email" label-style="!-mb-2 ml-2 z-10 text-xs" class="min-w-52"
                v-model="filters.email" />
              <input-base type="text" label="Filtrar por Telefone" label-style="!-mb-2 ml-2 z-10 text-xs"
                class="min-w-52" v-model="filters.phone" /> -->
              <input-base class="hidden sm:flex" :options="[
                { value: 'name', label: 'Nome' },
                { value: 'total_spent', label: 'Total Gasto' },
              ]" label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sort_by" />
              <input-base class="hidden sm:flex" :options="[
                { value: 'asc', label: 'Crescente' },
                { value: 'desc', label: 'Decrescente' },
              ]" label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button @click="filterModalOpen = true" size="sm" class="mr-3 btn-circle">
                  <AdjustmentsHorizontalIcon class="w-5 " />
                </base-button>
              </div>
            </div>
          </template>
          <!-- <template #src="{ value }">
            <td>
              <nuxt-img class="w-10 h-10 rounded-full" :src="value ||
                'https://static.vecteezy.com/system/resources/previews/019/896/008/original/male-user-avatar-icon-in-flat-design-style-person-signs-illustration-png.png'
                " alt="Profile image" />
            </td>
          </template> -->
          <template #name="{ row }">
            <td>
              {{ row.user.name }}
            </td>
          </template>
          <template #phone="{ row }">
            <td>
              {{ formatPhone(row.user.phone) }}
            </td>
          </template>
        </table-base>
      </div>
    </div>
    <base-dialog title="Cadastrar novo paciente" v-model="isOpened">
      <clients-register-form v-if="isOpened" @submit="handleFormSubmit" :service="selectedService" :loading />
    </base-dialog>
    <base-dialog title="Detalhes do Paciente" v-model="viewUserModal">
      <div v-if="selectedService" class="p-4">
        <div class="flex items-center gap-4 mb-4">

          <nuxt-img class="w-20 h-20 rounded-full" :src="selectedService.user.profile_photo_url" alt="Profile image" />
          <div>
            <h2 class="text-xl font-semibold">{{ selectedService.user.name }}</h2>
            <p class="text-gray-600">{{ selectedService.user.email }}</p>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Telefone</label>
            <p class="mt-1">{{ formatPhone(selectedService.user.phone) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Total Gasto</label>
            <p class="mt-1">{{ toBrl(selectedService.total_spent) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Agendamentos</label>
            <p class="mt-1">{{ selectedService.total_scheduled }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Paciente desde</label>
            <p class="mt-1">{{ new Date(selectedService.user.created_at).toLocaleDateString() }}</p>
          </div>
        </div>
      </div>
    </base-dialog>
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
          v-model="filters.name" />
        <input-base :options="[
          { value: 'name', label: 'Nome' },
          { value: 'total_spent', label: 'Total Gasto' },
        ]" label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sort_by" />
        <input-base :options="[
          { value: 'asc', label: 'Crescente' },
          { value: 'desc', label: 'Decrescente' },
        ]" label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";
import { ref, onMounted, watch } from "vue";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
const filterModalOpen = ref();
const totalItems = ref(0);
const services = ref([]);
const isOpened = ref(false);
const toast = useToast();
const loading = ref(false);
const per_page = ref(10)
const page = ref(1)
function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length !== 11) {
    throw new Error('O número de telefone deve conter 11 dígitos.');
  }
  const ddd = cleaned.slice(0, 2);
  const prefix = cleaned.slice(2, 7);
  const suffix = cleaned.slice(7);
  return `(${ddd}) ${prefix}-${suffix}`;
}
function handleViewUser(row: any) {
  selectedService.value = row;
  viewUserModal.value = true;
}
const selectedService = ref<any>(null);
const viewUserModal = ref(false);
const formattedPhone = formatPhone('81996977111');


const columns = [
  { label: "Nome", key: "name", sm: true },
  { label: "Telefone", key: "phone" },
  { label: "Total Gasto", key: "total_spent", type: 'currency' },
  { label: "Agendamentos", key: "total_scheduled" },
];

const filters = ref({
  name: "",
  email: "",
  phone: "",
  sort_by: "name",
  direction: "asc",
});
function handleFilter() {
  filterModalOpen.value = false
  getData()
}
async function handleFormSubmit(client: User) {
  try {
    loading.value = true;
    await api.post("/client", client);
    toast.success("Paciente cadastrado com sucesso!");
    selectedService.value = undefined;
    isOpened.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

async function getData() {
  try {
    loading.value = true;
    const { data } = await api("/clients", {
      params: {
        name: filters.value.name,
        email: filters.value.email,
        phone: filters.value.phone,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    services.value = data.data;
    totalItems.value = data.total;
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}
const debouncedFetch = () => useDelay(async () => await getData(), 500);

watch(
  filters,
  () => {
    if (filterModalOpen.value) return
    if (filters.value.name) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);
watch(
  [page],
  () => {
    getData();
  }
);
onMounted(async () => {
  await getData();
});
</script>
