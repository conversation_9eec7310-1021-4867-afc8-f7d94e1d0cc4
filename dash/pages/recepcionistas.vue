<template>
  <div class="w-full">
    <div class="container-table md:h-sm">
      <table-base
        title="Recepcionistas"
        :columns="columns"
        :rows="leads"
        @edit="handleEdit"
        @delete="deleteUser"
        @new="isOpened = true"
      >
        <template #src="{ value }">
          <td>
            <nuxt-img
              class="w-10 ml-4 h-10 rounded-full"
              :src="
                value ||
                'https://static.vecteezy.com/system/resources/previews/019/896/008/original/male-user-avatar-icon-in-flat-design-style-person-signs-illustration-png.png'
              "
              alt="Profile image"
            />
          </td>
        </template>
        <template #created_at="{ value }">
          <td>
            {{ dayjs(value).format("DD/MM/YYYY") }}
          </td>
        </template>
      </table-base>
    </div>
    <base-dialog title="Adicionar novo recepcionista" v-model="isOpened">
      <employees-register-form
        v-if="isOpened"
        @submit="handleFormSubmit"
        :data="selectedUser"
        :item="selectedUser"
        :selected-type="selectedType"
        :loading="userStore.loading"
      />
    </base-dialog>
    <loading :show="userStore.loading" />
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/users";
import dayjs from "dayjs";
import { useToast } from "vue-toast-notification";
import { type User } from "~/models/user";

const toast = useToast();
const isOpened = ref(false);
const selectedUser = ref<User>();
const selectedType = ref("admins");
const userStore = useUserStore();
const leads = ref<User[]>([]);
const columns = [
  { label: "Avatar", key: "src" },
  { label: "Nome", key: "name" },
  { label: "Email", key: "email" },
  { label: "Criado em", key: "created_at" },
];

async function handleFormSubmit(user: User) {
  try {
    userStore.loading = true;
    if (selectedUser.value?.id)
      await userStore.editUser("/professionals", user);
    else await userStore.createProfessional(user);
    toast.success(
      `Recepcionista ${
        selectedUser.value?.id ? "editado" : "adicionado"
      } com sucesso!`
    );
    getUsers();
  } catch (error) {
    toast.error("Erro ao adicionar recepcionista!");
  } finally {
    userStore.loading = false;
    isOpened.value = false;
  }
}

async function getUsers() {
  userStore.loading = true;
  const data = await userStore.getUsers("/professionals");
  leads.value = data;
  userStore.loading = false;
}

function handleEdit(user: User) {
  selectedUser.value = user;
  isOpened.value = true;
}

async function deleteUser(user: User) {
  try {
    userStore.loading = true;
    await userStore.deleteUser("/" + selectedType.value, user.id);
    toast.warning("Recepcionista deletado!");
    await getUsers();
  } catch (error) {
    toast.error("Erro ao deletar recepcionista!");
  } finally {
    userStore.loading = false;
  }
}

watch(selectedType, getUsers);
onMounted(async () => {
  getUsers();
});
</script>
