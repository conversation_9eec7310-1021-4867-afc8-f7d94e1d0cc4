<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <table-base :total-items title="Anotações" :columns="columns" hide-delete :rows="annotations"
        v-model:per_page="per_page" v-model:page="page" @edit="handleEdit" @delete="deleteAnnotation" @new="
          () => {
            isOpened = true;
            isEditing = false;
          }
        " :loading="loading" :actions="[{ name: 'Visualizar', action: handleViewAnnotation }]">
        <template #filter>
          <div class="flex gap-4 items-center">
            <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
              v-model="filters.title" />
            <input-base label="Ordem" class="hidden sm:flex" label-style="!-mb-2 ml-2 z-10 text-xs"
              v-model="filters.direction" :options="[
                { value: 'asc', label: 'Mai<PERSON> antigas' },
                { value: 'desc', label: 'Mais recentes' },
              ]" />
          </div>
        </template>
        <template #user="{ row }">
          {{ row.company_user.user.name }}
        </template>
        <template #appointment_date="{ value }">
          {{ dayjs(value).format("DD/MM/YYYY HH:mm") }}
        </template>
      </table-base>
    </div>

    <base-dialog :title="selectedAnnotation ? 'Editar anotação' : 'Nova anotação'" v-model="isOpened"
      @close="resetFormValues">
      <form v-if="isOpened" @submit.prevent="onSubmit" class="p-6 sm:pb-4">
        <div class="space-y-4">
          <input-base type="text" label="Título *" v-model="title" :error="errors.title" />

          <autocomplete v-model="user_id" :options="clients" force-open :loading="loadingClients" label="Paciente *"
            option-label="name" option-value="user_id" get-id class="w-full" input-classes="w-full"
            :error="errors.user_id" />

          <input-base type="select" label="Consulta" v-model="order_id" :error="errors.order_id" :options="userOrders"
            option-label="formatted_date" option-value="id" :disabled="!user_id" />

          <input-base type="textarea" label="Observação *" class="md:pb-10" v-model="description"
            :error="errors.description" rows="5" />
        </div>
        <div class="fixed bottom-2 md:bottom-4 left-0 px-4 md:px-10 w-full">
          <base-button type="submit" class="w-full mt-4" :loading="loading">
            {{ selectedAnnotation ? "Salvar anotação" : "Criar anotação" }}
          </base-button>
        </div>
      </form>
    </base-dialog>

    <base-dialog v-model="isViewModalOpen" large>
      <div v-if="selectedAnnotation" class="p-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="flex-1">
            <h2 class="text-2xl font-semibold">{{ selectedAnnotation.title }}</h2>
            <div class="text-gray-600 text-sm mt-1">
              Paciente: {{ selectedAnnotation.company_user?.user?.name }}
            </div>
          </div>
          <div class="text-right text-sm text-gray-600">
            {{ dayjs(selectedAnnotation.created_at).format("DD/MM/YYYY HH:mm") }}
          </div>
        </div>

        <div class="grid gap-6">
          <!-- Consulta relacionada -->
          <div v-if="selectedAnnotation.order_id" class="bg-gray-50 p-4 rounded-lg">
            <h3 class="font-medium text-gray-700 mb-2">Consulta Relacionada</h3>
            <p class="text-gray-600">
              Nº {{ selectedAnnotation.order_id }}
            </p>
          </div>

          <!-- Observações -->
          <div class="space-y-2">
            <h3 class="font-medium text-gray-700">Observações</h3>
            <div class="bg-white p-4 rounded-lg border whitespace-pre-wrap">
              {{ selectedAnnotation.description }}
            </div>
          </div>

          <!-- Metadata -->
          <div class="border-t pt-4 mt-4">
            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <span class="font-medium">Criado em:</span>
                {{ dayjs(selectedAnnotation.created_at).format("DD/MM/YYYY HH:mm") }}
              </div>
              <div>
                <span class="font-medium">Última atualização:</span>
                {{ dayjs(selectedAnnotation.updated_at).format("DD/MM/YYYY HH:mm") }}
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="flex justify-end gap-3 mt-6">
          <base-button @click="handleEdit(selectedAnnotation)" color="secondary">
            Editar
          </base-button>
          <base-button @click="isViewModalOpen = false">
            Fechar
          </base-button>
        </div> -->
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useToast } from "vue-toast-notification";
import { useForm } from "vee-validate";
import * as yup from "yup";
import { api } from "~/server/api";

const selectedAnnotation = ref();

const schema = yup.object({
  title: yup.string().required("Título é obrigatório"),
  description: yup.string().required("Observação é obrigatória"),
  user_id: yup.string().required("Paciente é obrigatório"),
  order_id: yup.string().nullable(),
  appointment_date: yup.string().nullable(),
});

const { handleSubmit, resetForm, defineField, errors, setValues } = useForm({
  validationSchema: schema,
  initialValues: {
    title: "",
    description: "",
    user_id: "",
    order_id: "",
  },
  validateOnMount: false,
});

const isEditing = ref(false);
const [title] = defineField("title");
const [description] = defineField("description");
const [user_id] = defineField("user_id");
const [order_id] = defineField("order_id");
const [appointment_date] = defineField("appointment_date");
const per_page = ref(10);
const page = ref(1);
const toast = useToast();
const isOpened = ref(false);
const annotations = ref([]);
const clients = ref([]);
const loading = ref(false);
const totalItems = ref(0);
const loadingClients = ref(false);
const userOrders = ref([]);
const isViewModalOpen = ref(false);

const filters = ref({
  title: "",
  direction: "desc",
});

const columns = [
  { label: "Título", key: "title", sm: true },
  { label: "Paciente", key: "user" },
  // { label: "Nº Consulta", key: "order_id" },
  { label: "Data", key: "appointment_date" },
];

function resetFormValues() {
  selectedAnnotation.value = undefined;
  resetForm();
  isViewModalOpen.value = false;
}

const onSubmit = handleSubmit(async (values) => {
  try {
    loading.value = true;
    if (selectedAnnotation.value) {
      await api.put(`/annotations/${selectedAnnotation.value.id}`, values);
      toast.success("Anotação atualizada com sucesso!");
    } else {
      await api.post("/annotations", values);
      toast.success("Anotação cadastrada com sucesso!");
    }
    isOpened.value = false;
    resetFormValues();
    await getAnnotations();
  } catch (err) {
    console.error(err);
    toast.error(
      selectedAnnotation.value
        ? "Erro ao atualizar anotação!"
        : "Erro ao cadastrar anotação!"
    );
  } finally {
    loading.value = false;
  }
});

async function getAnnotations() {
  try {
    loading.value = true;
    const { data } = await api("/annotations", {
      params: {
        title: filters.value.title,
        direction: filters.value.direction,
      },
    });
    annotations.value = data.data;
    totalItems.value = data.total;
  } catch (error) {
    toast.error("Erro ao carregar anotações!");
  } finally {
    loading.value = false;
  }
}

async function getClients() {
  try {
    loadingClients.value = true;
    const { data } = await api("/clients");
    clients.value = data.data.map((client) => {
      return { ...client.user, user_id: client.id };
    });
    loadingClients.value = false;
  } catch (error) {
    toast.error("Erro ao carregar lista de pacientes!");
  } finally {
    loadingClients.value = false;
  }
}

async function getUserOrders(userId: string) {
  try {
    const { data } = await api("/company-orders", {
      params: {
        user_id: userId,
      },
    });

    userOrders.value = data.data.map((order) => {
      if (order.status === 'canceled') return null;
      return {
        ...order,
        formatted_date: `${dayjs(order.appointments[0].start_date).format("DD/MM/YYYY HH:mm")}-${dayjs(order.appointments[0].end_date).format("HH:mm")} Pedido #${order.id}`,
      }
    }).filter(order => order);
  } catch (error) {
    toast.error("Erro ao carregar pedidos do paciente!");
    userOrders.value = [];
  }
}

watch(user_id, async (newValue) => {
  if (newValue) {
    await getUserOrders(newValue);
  } else {
    userOrders.value = [];
    order_id.value = "";
  }
});

function handleEdit(annotation) {
  selectedAnnotation.value = annotation;
  setValues({
    title: annotation.title,
    description: annotation.description,
    user_id: annotation.company_user_id,
    order_id: annotation.order_id,
  });
  isEditing.value = true;
  isOpened.value = true;
  isViewModalOpen.value = false;
}

async function deleteAnnotation(annotation) {
  try {
    loading.value = true;
    await api.delete(`/annotations/${annotation.id}`);
    toast.warning("Anotação deletada!");
    await getAnnotations();
  } catch (error) {
    toast.error("Erro ao deletar anotação!");
  } finally {
    loading.value = false;
  }
}

const debouncedFetch = () => useDelay(async () => await getAnnotations(), 500);

watch(
  filters,
  () => {
    if (filters.value.title) {
      debouncedFetch();
    } else {
      getAnnotations();
    }
  },
  { deep: true }
);
watch(order_id, () => {
  if (order_id.value) {
    const currentOrder = userOrders.value.find((order) => order.id == order_id.value);
    if (currentOrder) {
      appointment_date.value = dayjs(currentOrder.appointments[0].start_date).format("YYYY-MM-DD HH:mm");
    }
  }
});
watch(isOpened, () => {
  if (!isOpened.value) {
    resetFormValues();
  }
});

onMounted(() => {
  getAnnotations();
  getClients();
});

const handleViewAnnotation = (annotation) => {
  selectedAnnotation.value = annotation;
  isViewModalOpen.value = true;
};
</script>

<style scoped>
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
</style>
