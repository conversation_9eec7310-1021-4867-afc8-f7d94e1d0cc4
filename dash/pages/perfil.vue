<template>
  <div>
    <div class="container-table flex-col md:flex-row justify-center items-center overflow-auto">
      <div class="flex flex-col items-center gap-y-2 px-3 py-5 w-full max-w-xl">
        <div>
          <label for="file" class="cursor-pointer">
            <nuxt-img class="w-28 h-28 object-cover rounded-full mb-3 mx-auto" :src="image" alt="Profile image" />
            <input id="file" type="file" class="hidden" @change="uploadImage" />
          </label>
        </div>
        <!-- <nuxt-img class="w-36 h-36 object-cover bg-gray-200 rounded-full" :src="image" alt="Profile image" />
        <button @click="uploadImage" class="btn btn-primary btn-sm">
          Alterar Foto
        </button> -->
        <input-base class="w-full" label="Nome" placeholder="Nome" type="text" v-model="name" />
        <input-base class="w-full" label="Email" placeholder="Email" type="email" v-model="email" />
        <input-base class="w-full" label="Telefone" placeholder="Telefone" data-maska="(##) #####-####"
          v-model="phone" />
        <div @click="updateProfile" class="btn btn-primary mt-5">
          Atualizar Dados
        </div>
      </div>
    </div>
    <!-- <div
      class="hidden md:block bg-gray-500 w-[2px] divider divider-horizontal mx-5 my-auto h-full"
    ></div> -->

    <loading :show="false" />
  </div>
</template>
<script setup lang="ts">
import { userInfo } from "os";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useLoginStore } from "~/store/user";

const user = useLoginStore();

const toast = useToast();
const loading = ref(false);
const name = ref("");
const email = ref("");
const phone = ref("");

const newImage = ref();
const image = computed(() => {
  return user.userInfo.image
})
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
async function updateProfile() {
  loading.value = true;
  try {
    const payload = {
      name: name.value,
      email: email.value,
      phone: phone.value,
    }
    await api.put("/profile", payload);
    loading.value = false;
    toast.success("Perfil Atualizado");
  } finally {
    loading.value = false;
  }
}
async function uploadImage(e: Event) {
  const target = e.currentTarget as HTMLInputElement;
  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = async () => {
    user.userInfo.image = reader.result as string;
    await api.post("/update-photo", { image: reader.result });

  };
}
async function getProfile() {
  loading.value = true;
  const { data } = await api.get("/profile");
  name.value = data.name;
  email.value = data.email;
  phone.value = data.phone;
  // check if profile_photo_path start with http
  if (data.profile_photo_path && data.profile_photo_path.startsWith("http")) {
    user.userInfo.image = data.profile_photo_path;
  }
  else if (data.profile_photo_url) user.userInfo.image = data.profile_photo_url;
  loading.value = false;
}
onMounted(() => {
  getProfile();
});
</script>
