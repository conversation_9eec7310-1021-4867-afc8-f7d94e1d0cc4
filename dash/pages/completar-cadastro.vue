<template>
  <div class="flex flex-col overflow-hidden bg-[#fff] h-full">
    <div class="grid lg:grid-cols-2 h-full">
      <div class="hidden lg:block">
        <nuxt-img
          loading="lazy"
          :src="`https://psyplus.site/assets/criar-conta.jpg`"
          class="h-full w-full object-cover"
        />
      </div>
      <div class="flex px-2 flex-col w-full h-full overflow-hidden">
        <!-- <div class="text-center mt-3 md:mt-5 mb-0">
          <div class="text-2xl font-semibold">Quase lá</div>
          <div v-if="currentStep === 1" class="text-base">
            Configure os horários
          </div>
          <div v-if="currentStep === 2" class="text-base">
            Personalize seu app
          </div>
        </div> -->
        <template v-if="currentStep === 1">
          <div class="text-center mt-3 md:mt-6 mb-3">
            <div class="text-2xl font-semibold">Quase lá</div>
            <div v-if="currentStep === 1" class="text-base">
              Configure os horários
            </div>
          </div>
          <div class="flex-1 mt-2 flex flex-col items-center overflow-auto">
            <div
              v-for="(day, index) in daysOfWeek"
              :key="index"
              class="mb-3 md:mb-3 flex md:h-auto flex-col md:flex-row gap-2 md:gap-5 items-start"
            >
              <div class="flex items-center space-x-4">
                <label
                  :for="`switch-${index}`"
                  class="font-medium w-32 md:mt-7"
                >
                  {{ day.name }}
                </label>
                <input
                  :id="`switch-${index}`"
                  type="checkbox"
                  class="toggle toggle-primary md:mt-7"
                  v-model="day.isActive"
                />
              </div>

              <div class="flex items-center w-full gap-2">
                <div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <div>
                        <label class="block text-sm font-medium">Abre as</label>
                        <input-base
                          :disabled="!day.isActive"
                          type="time"
                          v-model="day.startTime1"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium"
                          >Fecha as</label
                        >
                        <input-base
                          :disabled="!day.isActive"
                          type="time"
                          v-model="day.endTime1"
                        />
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="day.hasBreak"
                    class="flex items-center mt-1 space-x-4"
                  >
                    <div>
                      <label class="block text-sm font-medium">Reabre as</label>
                      <input-base type="time" v-model="day.startTime2" />
                    </div>
                    <div>
                      <label class="block text-sm font-medium">Fecha as</label>
                      <input-base type="time" v-model="day.endTime2" />
                    </div>
                  </div>
                </div>
                <button
                  class="btn btn-circle btn-sm mt-6 btn-primary"
                  @click="toggleBreak(index)"
                  :disabled="!day.isActive"
                >
                  <ChevronUpDownIcon v-if="!day.hasBreak" class="w-7 h-7" />
                  <XMarkIcon v-else class="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
          <base-button
            :loading="loading"
            class="btn btn-primary w-full mt-auto mb-2"
            @click="handleSubmit"
            >Salvar</base-button
          >
        </template>
        <div v-if="currentStep === 2" class="flex-1 overflow-auto">
          <div class="pt-4 mx-auto">
            <label for="banner" class="block">
              <div class="relative h-40 md:h-56 w-full items-center">
                <nuxt-img
                  loading="lazy"
                  class="w-full mx-auto h-40 md:h-56 object-fill bg-gray-200"
                  :src="banner"
                  alt="Banner image"
                />
                <div
                  class="btn btn-sm btn-circle absolute bottom-1 right-1 cursor-pointer"
                >
                  <PencilSquareIcon class="w-5 h-5" />
                </div>
              </div>
              <input
                id="banner"
                type="file"
                class="hidden"
                @change="uploadBanner"
              />
            </label>

            <div class="mt-4">
              <label for="logo" class="flex justify-center">
                <div class="relative w-24 md:w-28 h-24 md:h-28 items-center">
                  <nuxt-img
                    loading="lazy"
                    class="w-24 md:w-28 mx-auto h-24 md:h-28 object-cover bg-gray-200 rounded-full"
                    :src="image"
                    alt="Logo image"
                  />
                  <div
                    class="btn btn-sm btn-circle absolute bottom-1 right-1 cursor-pointer"
                  >
                    <PencilSquareIcon class="w-5 h-5" />
                  </div>
                </div>
                <input
                  id="logo"
                  type="file"
                  class="hidden"
                  @change="uploadImage"
                />
              </label>
            </div>

            <div class="flex gap-2 mt-5">
              <div class="btn btn-outline w-1/2" @click="currentStep = 1">
                Voltar
              </div>
              <div class="btn btn-primary w-1/2" @click="router.push('/')">
                Finalizar
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useToast } from "vue-toast-notification";
import { ChevronUpDownIcon, XMarkIcon } from "@heroicons/vue/24/solid";
import { api } from "~/server/api";
import dayjs from "dayjs";
// import { useCompanyStore } from "~/store/company";
// import { useLoginStore } from "~/store/user";
const runtimeConfig = useRuntimeConfig();
const router = useRouter();
const currentStep = ref(1);
const daysOfWeek = ref([
  {
    name: "Segunda-feira",
    key: "monday_time",
    pause_key: "monday_pause",
    isActive: true,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Terça-feira",
    key: "tuesday_time",
    pause_key: "tuesday_pause",
    isActive: true,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Quarta-feira",
    key: "wednesday_time",
    pause_key: "wednesday_pause",
    isActive: true,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Quinta-feira",
    key: "thursday_time",
    pause_key: "thursday_pause",
    isActive: true,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Sexta-feira",
    key: "friday_time",
    pause_key: "friday_pause",
    isActive: true,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Sábado",
    key: "saturday_time",
    pause_key: "saturday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Domingo",
    key: "sunday_time",
    pause_key: "sunday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
]);
const toast = useToast();
const image = ref(`${useRuntimeConfig().public.API_URL}/icons/logo.png`);
const banner = ref(
  "https://plataformaicd.com/wp-content/plugins/tutor/assets/images/placeholder.svg"
);
const loading = ref(false);

function toggleBreak(index: number) {
  const day = daysOfWeek.value[index];
  day.hasBreak = !day.hasBreak;
  if (day.hasBreak) {
    day.endTime1 = "12:00";
  } else {
    day.endTime1 = "18:00";
  }
}

async function handleSubmit() {
  const payload = {} as { [key: string]: string | null };
  daysOfWeek.value.forEach((day) => {
    if (day.isActive) {
      if (day.hasBreak) {
        payload[day.key] = `${day.startTime1}-${day.endTime2}`;
        payload[day.pause_key] = `${day.endTime1}-${day.startTime2}`;
      } else {
        payload[day.key] = `${day.startTime1}-${day.endTime1}`;
        payload[day.pause_key] = null;
      }
    } else {
      payload[day.pause_key] = null;
      payload[day.key] = null;
    }
  });
  try {
    loading.value = true;
    await api.post("/update-time", payload);
    router.push("/");
    toast.success("Cadastro realizado! Aproveite!");
    // currentStep.value = 2;
  } catch (error) {
    console.error("Erro ao salvar os horários", error);
  } finally {
    loading.value = false;
  }
}

async function uploadImage(e: Event) {
  const target = e.currentTarget as HTMLInputElement;
  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);

  reader.onload = async () => {
    image.value = reader.result as string;
    await api.post("/update-logo", { image: reader.result });
  };
}

async function uploadBanner(e: Event) {
  const target = e.currentTarget as HTMLInputElement;
  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);

  reader.onload = async () => {
    banner.value = reader.result as string;
    await api.post("/update-banner", { image: reader.result });
  };
}
</script>
<style></style>
