<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base title="Produtos" :columns="columns" v-model:per_page="per_page" v-model:page="page" :rows="services"
          class="flex-1" @new="handleNew" @edit="handleEdit" @delete="deleteServiceById" :loading="loading" :actions="[
            { name: 'Adicionar Estoque', action: handleAddStock }
          ]">
          <template #filter>
            <div class="flex gap-4 items-center">
              <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
                v-model="filters.name" />
              <input-base class="hidden sm:flex" :options="[
                { value: 'quantity', label: 'Estoque' },
                { value: 'name', label: 'Nome' },
                { value: 'base_price', label: 'Preço Base' },
              ]" label-style="!-mb-2 ml-2 z-10 text-xs" label="Ordenar por" v-model="filters.sort_by" />
              <input-base class="hidden sm:flex" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction"
                :options="[
                  { value: 'asc', label: 'Crescente' },
                  { value: 'desc', label: 'Decrescente' },
                ]" label="Ordem" />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button @click="filterModalOpen = true" size="sm" class="mr-3 btn-circle">
                  <AdjustmentsHorizontalIcon class="w-5 " />
                </base-button>
              </div>
            </div>
          </template>

          <template #duration="{ value }">
            <td class="hidden sm:table-cell">
              {{ handleTime(value) }}
            </td>
          </template>
        </table-base>
      </div>
    </div>
    <base-dialog title="Adicionar Produto" large v-model="isOpened">
      <products-register-form @edit-quantity="stockModalOpen = true" v-if="isOpened" @submit="handleFormSubmit" :product="selectedProduct"
        :loading="loading" />
    </base-dialog>
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
          v-model="filters.name" />
        <input-base :options="[
          { value: 'quantity', label: 'Estoque' },
          { value: 'name', label: 'Nome' },
          { value: 'base_price', label: 'Preço Base' },
        ]" label-style="!-mb-2 ml-2 z-10 text-xs" label="Ordenar por" v-model="filters.sort_by" />
        <input-base label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" :options="[
          { value: 'asc', label: 'Crescente' },
          { value: 'desc', label: 'Decrescente' },
        ]" label="Ordem" />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
    <base-dialog title="Adicionar Estoque" v-model="stockModalOpen">
      <ProductsMovementRegisterForm v-if="stockModalOpen" @submit="handleStockSubmit" :product="selectedProduct"
        :loading="loading" />
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useToast } from "vue-toast-notification";
import { handleTime } from "@/utils/formatFunctions";
import { api } from "~/server/api";
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";
const filterModalOpen = ref();
const stockModalOpen = ref(false);
const services = ref<Product[]>([]);
const selectedProduct = ref<Product>();
const loading = ref(false);
const isOpened = ref(false);
const toast = useToast();
const per_page = ref(10)
const page = ref(1)
const columns = [
  { label: "Nome", key: "name", sm: true },
  { label: "Estoque", key: "quantity" },
  { label: "Preço", key: "base_price", type: "currency" },
];

const filters = ref({
  name: "",
  sort_by: "name",
  direction: "asc",
});

function handleFilter() {
  filterModalOpen.value = false
  getData()
}
const debouncedFetch = () => useDelay(async () => await getData(), 500);

function handleAddStock(product: Product) {
  selectedProduct.value = product;
  stockModalOpen.value = true;
}

async function handleStockSubmit(values: any) {
  try {
    loading.value = true;
    await api.post('/movements', {
      ...values,
      product_id: selectedProduct.value?.id
    });
    toast.success('Estoque atualizado com sucesso!');
    stockModalOpen.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error('Erro ao atualizar estoque!');
  } finally {
    loading.value = false;
  }
}

async function handleFormSubmit(product: Product) {
  try {
    loading.value = true;
    if (selectedProduct.value?.id) {
      const currentVariants = selectedProduct.value.variants;
      const currentVariantMap = new Map(currentVariants?.map((v) => [v.id, v]));
      const deleted_variants: Variant[] = [];
      const new_variants: Variant[] = [];
      const updated_variants: Variant[] = [];
      product.variants?.forEach((variant) => {
        if (!variant.id) {
          new_variants.push(variant);
        } else if (currentVariantMap.has(variant.id)) {
          const existingVariant = currentVariantMap.get(variant.id);
          if (
            existingVariant &&
            (existingVariant.name !== variant.name ||
              existingVariant.price !== variant.price ||
              existingVariant.quantity !== variant.quantity)
          ) {
            updated_variants.push(variant);
          }
          currentVariantMap.delete(variant.id);
        }
      });
      deleted_variants.push(...currentVariantMap.values());
      const formatService = {
        ...product,
        base_price: product.variants?.length ? undefined : product.base_price,
        quantity: product.variants?.length ? undefined : product.base_price,
        variants: product.variants?.length ? product.variants : undefined,
        status: "active",
        deleted_variants,
        updated_variants,
        new_variants,
      };
      await api.put(`/products/${product.id}`, formatService);
      toast.success("Serviço atualizado com sucesso!");
    } else {
      const formatService = {
        ...product,
        base_price: product.variants?.length ? 0 : product.base_price,
        quantity: product.variants?.length ? 0 : product.quantity,
        variants: product.variants?.length ? product.variants : undefined,
        status: "active",
      };
      await api.post(`/products`, formatService);
      toast.success("Serviço criado com sucesso!");
    }
    selectedProduct.value = undefined;
    isOpened.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

async function deleteServiceById(item: Product) {
  try {
    if (item.id) {
      await api.delete(`/products/${item.id}`);
      toast.warning("Produto deletado!");
    }
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  }
}

function handleEdit(product: Product) {
  selectedProduct.value = product;
  isOpened.value = true;
}

function handleNew() {
  selectedProduct.value = undefined;
  isOpened.value = true;
}

async function getData() {
  try {
    loading.value = true;
    const { data } = await api("/products", {
      params: {
        name: filters.value.name,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    services.value = data.data;
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

watch(
  [filters],
  ([filtersVal]) => {
    if (filterModalOpen.value) return
    if (filtersVal.name) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

onMounted(async () => {
  await getData();
});
</script>
