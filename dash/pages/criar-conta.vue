<template>
  <div class="flex flex-col bg-[#fff] h-full">
    <div class="grid lg:grid-cols-2 h-full">
      <nuxt-img
        src="https://psyplus.site/assets/criar-conta.webp"
        loading="lazy"
        class="h-full mt-auto hidden lg:block"
      />
      <div class="flex flex-col px-7 max-w-lg mx-auto w-full lg:px-10 pt-4 md:pt-10">
        <div class="">
          <div class="text-center mb-5">
            <div class="text-2xl font-semibold">Junte-se a nós</div>
            <div class="text-lg mt-2">Realize seu cadastro e aproveite</div>
          </div>
          <div v-if="incorrectCredentials" class="text-red-500 text-center mb-5">
            Senha incorreta
          </div>
          <div v-if="emailAlreadyTaken" class="text-red-500 text-center mb-5">
            Email já cadastrado
          </div>  
          <create-company-register-form :loading="loading" @submit="handleSubmit" :incorrect-credentials="incorrectCredentials" :email-already-taken="emailAlreadyTaken" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";
import { useLoginStore } from "~/store/user";
const runtimeConfig = useRuntimeConfig();
const incorrectCredentials = ref(false);
const emailAlreadyTaken = ref(false);
const router = useRouter();
const loading = ref(false);
async function handleSubmit(val: Company) {
  try {
    loading.value = true;
    incorrectCredentials.value = false;
    const user = useLoginStore();
    const company = useCompanyStore();
    const { data } = await api.post("create-company", val);
    user.userInfo = data.user;
    user.token = data.token;
    company.name = data.company.name;
    company.slug = data.company.slug;
    router.push("/completar-cadastro");
  } catch (err) {
    if (err.response.data.message === 'Invalid credentials') {
      incorrectCredentials.value = true;
    }
    if(err.response.data.message === 'The email has already been taken.') {
      emailAlreadyTaken.value = true;
    }
  } finally {
    loading.value = false;
  }
}
</script>
