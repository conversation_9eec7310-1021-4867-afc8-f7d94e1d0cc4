<template>
  <div class="">
    <div class="container-table p-0 flex-col md:flex-row overflow-auto">
      <div class="flex h-full flex-col w-full">
        <div class="md:grid md:grid-cols-3 lg:grid-cols-5 gap-2 flex-1 overflow-auto">
          <label for="banner" class="col-span-5">
            <div class="relative h-40 md:h-56 w-full items-center">
              <nuxt-img loading="lazy" class="w-full mx-auto h-36 md:h-56 object-fill bg-gray-200" :src="banner"
                alt="Profile image">
              </nuxt-img>
              <div class="absolute -bottom-14 left-3 md:left-4">
                <label for="file" class="flex justify-center">
                  <div class="relative w-24 md:w-28 h-24 md:h-28 items-center">
                    <nuxt-img loading="lazy"
                      class="w-24 md:w-28 mx-auto h-24 md:h-28 object-cover bg-gray-200 rounded-full" :src="image"
                      alt="Profile image">
                    </nuxt-img>
                    <div class="btn btn-sm btn-circle absolute bottom-1 right-1 cursor-pointer">
                      <PencilSquareIcon class="w-5 h-5" />
                    </div>
                  </div>
                  <input id="file" type="file" class="hidden" @change="uploadImage" />
                </label>
              </div>
              <div class="btn btn-sm btn-circle absolute bottom-1 right-1 cursor-pointer">
                <PencilSquareIcon class="w-5 h-5" />
              </div>
            </div>
            <input id="banner" type="file" class="hidden" @change="uploadBanner" />
          </label>

          <div class="w-full md:-mt-0 -mt-5 px-2 md:px-4 gap-2 flex flex-col col-span-1 md:col-span-3 lg:col-span-5">
            <input-base v-if="!company.solo_professional" class="pl-28 md:pl-32 mt-2 md:mt-0" label="Nome do negócio"
              placeholder="Nome do negócio" type="text" v-model="name" />
            <div class="flex flex-col lg:flex-row gap-2">
              <input-base class="pl-28 md:pl-32 mt-2 md:mt-0 w-full" label="Identificador do negócio"
                placeholder="Identificador do negócio" type="text" v-model="slug" />
              <input-base class="w-full" label="Link de acesso dos pacientes"
                inputClasses="cursor-pointer text-sm px-1 sm:text-base md:px-2 sm:pl-4" type="text" readonly
                v-model="link">
                <template #append>
                  <Square2StackIcon @click="handleCopy"
                    class="sm:w-8 sm:h-8 w-8 h-8 text-gray-600 absolute inset-y-0 right-1 my-auto md:right-2 cursor-pointer" />
                </template>
              </input-base>
            </div>
            <div class="flex flex-col sm:flex-row gap-2">
              <input-base disabled data-maska="##/######" v-if="company.solo_professional" class="w-full" label="CRP" placeholder="CRP" type="text"
                v-model="CRP" />
              <input-base class="w-full" label="Usuário do instagram" placeholder="Pessoa_23" type="text"
                v-model="instagram" />
              <input-base data-maska="(##) # ####-####" placeholder="(00) 0 0000-0000" class="w-full"
                label="Número do whatsapp" type="text" v-model="whatsapp">
              </input-base>
            </div>
            <div class="">

              <string-tag-select v-model="selectedSpecialties" :options="availableSpecialties"
                label="Especialidades" placeholder="Selecione as especialidades" name="specialties"
                class="mb-4" />

              <string-tag-select v-model="selectedClientTypes" :options="availableClientTypes"
                label="Tipos de Atendimento" placeholder="Selecione os tipos de atendimento" name="client_types" />
            </div>

            <input-base v-model="about" type="textarea" label="Descrição breve do negócio" />
          </div>
        </div>
        <div class="w-full px-2 pb-2">
          <base-button :loading @click="updateProfile" class="mt-4 w-full">
            Atualizar Dados
          </base-button>
        </div>
      </div>
    </div>
    <!-- <loading :show="false" /> -->
  </div>
</template>
<script setup lang="ts">
import { Square2StackIcon } from "@heroicons/vue/24/outline";
import {
  PencilSquareIcon,
} from "@heroicons/vue/24/solid";
import StringTagSelect from "~/components/StringTagSelect.vue";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";
import { useLoginStore } from "~/store/user";
const user = useLoginStore();
const company = useCompanyStore();
const toast = useToast();
const loading = ref(false);
const name = ref("");
const instagram = ref("");
const whatsapp = ref("");
const about = ref("");
const buttonsColor = ref("");
const backgroundColor = ref("");
const cardsColor = ref("");
const image = ref();
const banner = ref(`${useRuntimeConfig().public.API_URL}/no-banner.webp`);
const slug = ref("");
const CRP = ref("");

// Specialties for psychologists as strings
const availableSpecialties = ref<string[]>([
  'Ansiedade',
  'Depressão',
  'Transtorno Bipolar',
  'Transtorno Obsessivo-Compulsivo (TOC)',
  'Estresse Pós-Traumático (TEPT)',
  'Transtornos Alimentares',
  'Fobias',
  'TDA/TDAH',
  'Transtorno do Espectro Autista (TEA)',
  'Esquizofrenia',
  'Transtornos de Personalidade',
  'Dependência Química',
  'Luto',
  'Estresse',
  'Burnout',
  'Problemas de Relacionamento',
  'Terapia de Casal',
  'Terapia Familiar',
  'Orientação Parental',
  'Orientação Profissional',
  'Desenvolvimento Pessoal',
  'Autoestima',
  'Habilidades Sociais',
  'Transtornos do Sono',
  'Disfunções Sexuais',
  'Questões de Gênero e Sexualidade',
  'Transtornos Psicossomáticos',
  'Neuropsicologia',
  'Psicologia Esportiva',
  'Psicologia Organizacional'
]);
const selectedSpecialties = ref<string[]>([]);

// Client types as strings
const availableClientTypes = ref<string[]>([
  'Infantil',
  'Adolescente',
  'Adulto',
  'Idoso',
  'Casal',
]);
const selectedClientTypes = ref<string[]>([]);
const daysOfWeek = ref([
  {
    name: "Segunda-feira",
    key: "monday_time",
    pause_key: "monday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Terça-feira",
    key: "tuesday_time",
    pause_key: "tuesday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Quarta-feira",
    key: "wednesday_time",
    pause_key: "wednesday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Quinta-feira",
    key: "thursday_time",
    pause_key: "thursday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Sexta-feira",
    key: "friday_time",
    pause_key: "friday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Sábado",
    key: "saturday_time",
    pause_key: "saturday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Domingo",
    key: "sunday_time",
    pause_key: "sunday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
]);
const link = computed(() => `psyplus.site/${slug.value}`);
async function updateProfile() {
  loading.value = true;
  try {
    const payload = {
      name: name.value,
      slug: slug.value,
      buttons_color: buttonsColor.value,
      background_color: backgroundColor.value,
      cards_color: cardsColor.value,
      instagram: instagram.value,
      whatsapp: whatsapp.value,
      about: about.value,
      specialties: selectedSpecialties.value.join(','),
      public: selectedClientTypes.value.join(','),
    } as { [key: string]: string | string[] | null };

    daysOfWeek.value.forEach((day) => {
      if (day.isActive) {
        if (day.hasBreak) {
          payload[day.key] = `${day.startTime1}-${day.endTime2}`;
          payload[day.pause_key] = `${day.endTime1}-${day.startTime2}`;
        } else {
          payload[day.key] = `${day.startTime1}-${day.endTime1}`;
          payload[day.pause_key] = null;
        }
      } else {
        payload[day.key] = null;
        payload[day.pause_key] = null;
      }
    });
    await api.put("/my-company", payload);
    loading.value = false;
    updateStorage();
    toast.success("Atualizado");
  } finally {
    loading.value = false;
  }
}
function updateStorage() {
  company.name = name.value;
  company.buttons_color = buttonsColor.value;
  company.background_color = backgroundColor.value;
  company.cards_color = cardsColor.value;
  company.instagram = instagram.value;
  company.whatsapp = whatsapp.value;
  company.specialties = selectedSpecialties.value;
  company.client_types = selectedClientTypes.value;
  company.about = about.value;
  company.slug = slug.value;
  daysOfWeek.value.forEach((day) => {
    if (day.isActive) {
      if (day.hasBreak) {
        // @ts-expect-error - Dynamic property access
        company[day.key] = `${day.startTime1}-${day.endTime2}`;
        // @ts-expect-error - Dynamic property access
        company[day.pause_key] = `${day.endTime1}-${day.startTime2}`;
      } else {
        // @ts-expect-error - Dynamic property access
        company[day.key] = `${day.startTime1}-${day.endTime1}`;
        // @ts-expect-error - Dynamic property access
        company[day.pause_key] = null;
      }
    } else {
      // @ts-expect-error - Dynamic property access
      company[day.key] = null;
      // @ts-expect-error - Dynamic property access
      company[day.pause_key] = null;
    }
  });
}

async function uploadImage(e: Event) {
  const target = e.currentTarget as HTMLInputElement;

  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);

  reader.onload = async () => {
    image.value = reader.result as string;
    if (company.solo_professional) {
      await api.post("/update-photo", { image: reader.result });
      user.userInfo.image = reader.result as string;

    }
    else {
      const { data } = await api.post("/update-logo", { image: reader.result });
      const path = data.logos["192"].split("/")[3];
      company.logo = `${useRuntimeConfig().public.API_URL}/logos/${path}`;
    }
  };
}

async function uploadBanner(e: Event) {
  const target = e.currentTarget as HTMLInputElement;
  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = async () => {
    banner.value = reader.result as string;
    const { data } = await api.post("/update-banner", { image: reader.result });
    const path = data.banner_url.split("/")[3];
    company.banner = `${useRuntimeConfig().public.API_URL}/banners/${path}`;
  };
}
async function getProfile() {
  loading.value = true;
  name.value = company.name;
  buttonsColor.value = company.buttons_color;
  slug.value = company.slug;
  backgroundColor.value = company.background_color;
  cardsColor.value = company.cards_color;
  instagram.value = company.instagram;
  whatsapp.value = company.whatsapp;
  about.value = company.about;
  CRP.value = company.CRP;
  if (company.specialties && Array.isArray(company.specialties)) {
    selectedSpecialties.value = company.specialties;
  }

  if (company.client_types && Array.isArray(company.client_types)) {
    selectedClientTypes.value = company.client_types;
  }
  if (company.solo_professional) {
    image.value = user.userInfo.image
  }
  else if (company.logo) image.value = company.logo;
  else
    image.value = `${useRuntimeConfig().public.API_URL}/icons/logo.png`;
  if (company.banner) banner.value = company.banner;
  else banner.value = `${useRuntimeConfig().public.API_URL}/no-banner.webp`;
  daysOfWeek.value = daysOfWeek.value.map((day) => {
    // @ts-expect-error - Dynamic property access
    const time = company[day.key];
    // @ts-expect-error - Dynamic property access
    const pause = company[day.pause_key];
    if (time) {
      const [startTime, endTime] = time.split("-");
      day.startTime1 = startTime;
      if (pause) {
        const [pauseStart, pauseEnd] = pause.split("-");
        day.endTime1 = pauseStart;
        day.startTime2 = pauseEnd;
        day.endTime2 = endTime;
        day.hasBreak = true;
      } else {
        day.endTime1 = endTime;
        day.hasBreak = false;
      }
      day.isActive = true;
    }
    return day;
  });
  loading.value = false;
}

function handleCopy() {
  navigator.clipboard.writeText(link.value);
  toast.success("Texto copiado");
}
onMounted(() => {
  getProfile();
});
watch(slug, () => {
  slug.value = slug.value.replace(/ /g, "-").toLowerCase();
  slug.value = slug.value
    .replace("@", "a")
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/[^a-zA-Z0-9-]/g, "");
});
watch(company, () => {
  getProfile();
});

// Function removed as it was unused
</script>
