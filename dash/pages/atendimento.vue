<template>
  <div
    class="container-table w-full h-sm gap-4"
    :class="user.isWorker && ''"
  >
    <Service
      :user="data.user"
      :service="data.service"
      :profissional_id="data.profissional_id"
      :created_at="data.created_at"
      observations="Alguma observação"
    />
    <div class="mt-auto">
      <BaseButton class="w-full mb-2">Finalizar atendimento</BaseButton>
      <BaseButton class="w-full btn-error" color="secondary">Cancelar atendimento</BaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from "vue-toast-notification";
import type { Service } from "~/models/services";
import type { User } from "~/models/user";
import { useLoginStore } from "~/store/user";
const user = useLoginStore().userInfo;

type ProfessionalService = {
  id: string;
  user: User;
  service: Service;
  profissional_id: string;
  created_at: string;
};

const data = {
  id: "1",
  service: {
    name: "Corte de Cabelo",
    duration: 120,
  },
  user: {
    name: "<PERSON>",
    image: "http://placehold.it/120x120&text=image1",
  },

  profissional_id: "101",
  created_at: "23:40",
} as ProfessionalService;

const toast = useToast();
const orders = ref<ProfessionalService | null>(null);

async function getData() {
  try {
    orders.value = data;
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  }
}
onMounted(async () => {
  await getData();
});
</script>
