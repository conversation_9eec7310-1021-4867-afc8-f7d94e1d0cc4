<template>
  <div class="">
    <div class="container-table mx-1 px-1 sm:px-1 sm:mx-1 overflow-auto">
      <div class="flex flex-col items-center md:items-start md:flex-row w-full">
        <div class="flex flex-col pr-2 w-48">
          <b class="text-center"> Logo </b>
          <label for="file" class="flex justify-center">
            <div class="relative w-28 items-center">
              <nuxt-img
                class="w-28 mx-auto h-28 object-cover bg-gray-200 rounded-full"
                :src="image"
                loading="lazy"
                alt="Profile image"
              >
              </nuxt-img>
              <div
                class="btn btn-sm btn-circle absolute bottom-1 right-1 cursor-pointer"
              >
                <PencilSquareIcon class="w-5 h-5" />
              </div>
            </div>
            <input id="file" type="file" class="hidden" @change="uploadImage" />
          </label>
        </div>
        <div
          class="w-full mb-4 gap-2 flex flex-wrap flex-col md:col-span-2 lg:col-span-4 pr-5"
        >
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full">
            <input-base
              class="w-full"
              label="Cor dos Botões"
              inputClasses="p-0 cursor-pointer border-0"
              placeholder="Cor dos Botões"
              v-model="buttonsColor"
              type="color"
            />
            <input-base
              class="w-full"
              label="Cor dos cards"
              placeholder="Cor dos cards"
              inputClasses="p-0 cursor-pointer border-0"
              v-model="cardsColor"
              type="color"
            />
            <input-base
              class="w-full"
              label="Cor de fundo"
              placeholder="Cor de fundo"
              inputClasses="p-0 cursor-pointer border-0"
              v-model="backgroundColor"
              type="color"
            />
          </div>
        </div>
        <MockPhone
          :buttonsColor
          :contrastText
          :backgroundColor
          :image
          :cardsColor
          class="sm:min-w-[320px]"
        >
          <div class="px-1 gap-1 flex flex-col overflow-hidden">
            <div class="px-4 pt-2 pb-2">
              <input-base
                type="text"
                readonly
                label-style="!-mb-2 ml-2 z-10 text-xs"
                class="sm:min-w-52"
                label="Filtrar serviços"
              />
            </div>
            <mock-card
              v-for="(service, i) in services"
              :key="i"
              :service
              :background="cardsColor"
              :buttonsColor
              :contrastText
            ></mock-card>
          </div>
        </MockPhone>
      </div>

      <base-button :loading @click="updateProfile" class="mt-2 md:mt-auto">
        Atualizar Dados
      </base-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PencilSquareIcon } from "@heroicons/vue/24/solid";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";
const services = ref([]);
const toast = useToast();
const loading = ref(false);
const buttonsColor = ref("");
const backgroundColor = ref("");
const cardsColor = ref("");
const company = useCompanyStore();
const image = ref(
  "https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg"
);
const newImage = ref();
async function updateProfile() {
  loading.value = true;
  try {
    const payload = {
      buttons_color: buttonsColor.value,
      background_color: backgroundColor.value,
      cards_color: cardsColor.value,
    };
    await api.put("/my-company", payload);
    loading.value = false;
    company.cards_color = cardsColor.value;
    company.background_color = backgroundColor.value;
    company.buttons_color = buttonsColor.value;

    toast.success("Perfil Atualizado");
  } finally {
    loading.value = false;
  }
}
async function uploadImage(e: Event) {
  const target = e.currentTarget as HTMLInputElement;
  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = async () => {
    image.value = reader.result as string;
    await api.post("/update-logo", { image: reader.result });
  };
  newImage.value = file;
}
async function getProfile() {
  backgroundColor.value = company.background_color;
  buttonsColor.value = company.buttons_color;
  cardsColor.value = company.cards_color;
  image.value = company.logo;
}
onMounted(() => {
  getProfile();
  getServices();
});
async function getServices() {
  const { data } = await api("/services");
  services.value = data.data;
}
function getContrastColor(hex: string): number {
  hex = hex.replace("#", "");
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
  return luminance > 130 ? 0 : 1;
}

const contrastText = computed(() => getContrastColor(cardsColor.value));
</script>
