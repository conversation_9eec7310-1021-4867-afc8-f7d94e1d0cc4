<template>
  <div class="">
    <div class="container-table p-0 relative flex-col overflow-auto">
      <!-- <div class="text-center text-lg md:text-xl mt-2">
        Hor<PERSON>rios de funcionamento
      </div> -->
      <div class="pt-4 md:gap-4 md:pt-8 px-2 md:px-4 grid grid-flow-row">
        <div
          v-for="(day, index) in daysOfWeek"
          :key="index"
          class="mb-3 md:mb-3 flex flex-col sm:flex-row gap-2 sm:gap-10 md:gap-5 items-center mx-auto"
        >
          <div class="flex items-center space-x-4">
            <label :for="`switch-${index}`" class="font-medium w-32 sm:mt-7">
              {{ day.name }}
            </label>
            <input
              :id="`switch-${index}`"
              type="checkbox"
              class="toggle toggle-primary sm:mt-7"
              v-model="day.isActive"
            />
          </div>

          <div class="flex items-center w-full gap-2">
            <div>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div>
                    <label class="block text-sm font-medium">Abre as</label>
                    <input-base
                      :disabled="!day.isActive"
                      type="time"
                      v-model="day.startTime1"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium">Fecha as</label>
                    <input-base
                      :disabled="!day.isActive"
                      type="time"
                      v-model="day.endTime1"
                    />
                  </div>
                </div>
              </div>

              <div v-if="day.hasBreak" class="flex items-center mt-1 space-x-4">
                <div>
                  <label class="block text-sm font-medium">Reabre as</label>
                  <input-base type="time" v-model="day.startTime2" />
                </div>
                <div>
                  <label class="block text-sm font-medium">Fecha as</label>
                  <input-base type="time" v-model="day.endTime2" />
                </div>
              </div>
            </div>
            <button
              class="btn btn-circle btn-sm mt-6 btn-primary"
              @click="toggleBreak(index)"
              :disabled="!day.isActive"
            >
              <ChevronUpDownIcon v-if="!day.hasBreak" class="w-7 h-7" />
              <XMarkIcon v-else class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
      <div class="sticky mt-auto bottom-0 bg-base-100 px-2 pb-2">
        <base-button
          :loading="loading"
          @click="updateSchedules"
          class="mt-4 w-full"
        >
          Atualizar Horários
        </base-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronUpDownIcon, XMarkIcon } from "@heroicons/vue/24/solid";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";

const company = useCompanyStore();
const toast = useToast();
const loading = ref(false);

const daysOfWeek = ref([
  {
    name: "Segunda-feira",
    key: "monday_time",
    pause_key: "monday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Terça-feira",
    key: "tuesday_time",
    pause_key: "tuesday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Quarta-feira",
    key: "wednesday_time",
    pause_key: "wednesday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Quinta-feira",
    key: "thursday_time",
    pause_key: "thursday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Sexta-feira",
    key: "friday_time",
    pause_key: "friday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Sábado",
    key: "saturday_time",
    pause_key: "saturday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
  {
    name: "Domingo",
    key: "sunday_time",
    pause_key: "sunday_pause",
    isActive: false,
    startTime1: "08:00",
    endTime1: "18:00",
    startTime2: "14:00",
    endTime2: "18:00",
    hasBreak: false,
  },
]);

async function updateSchedules() {
  loading.value = true;
  try {
    const payload = {} as { [key: string]: string | null };

    daysOfWeek.value.forEach((day) => {
      if (day.isActive) {
        if (day.hasBreak) {
          payload[day.key] = `${day.startTime1}-${day.endTime2}`;
          payload[day.pause_key] = `${day.endTime1}-${day.startTime2}`;
        } else {
          payload[day.key] = `${day.startTime1}-${day.endTime1}`;
          payload[day.pause_key] = null;
        }
      } else {
        payload[day.key] = null;
        payload[day.pause_key] = null;
      }
    });

    await api.post("/update-time", payload);
    updateStorage();
    toast.success("Horários atualizados");
  } finally {
    loading.value = false;
  }
}

function updateStorage() {
  daysOfWeek.value.forEach((day) => {
    if (day.isActive) {
      if (day.hasBreak) {
        company[day.key] = `${day.startTime1}-${day.endTime2}`;
        company[day.pause_key] = `${day.endTime1}-${day.startTime2}`;
      } else {
        company[day.key] = `${day.startTime1}-${day.endTime1}`;
        company[day.pause_key] = null;
      }
    } else {
      company[day.key] = null;
      company[day.pause_key] = null;
    }
  });
}

function getSchedules() {
  loading.value = true;
  daysOfWeek.value = daysOfWeek.value.map((day) => {
    const time = company[day.key];
    const pause = company[day.pause_key];
    if (time) {
      const [startTime, endTime] = time.split("-");
      day.startTime1 = startTime;
      if (pause) {
        const [pauseStart, pauseEnd] = pause.split("-");
        day.endTime1 = pauseStart;
        day.startTime2 = pauseEnd;
        day.endTime2 = endTime;
        day.hasBreak = true;
      } else {
        day.endTime1 = endTime;
        day.hasBreak = false;
      }
      day.isActive = true;
    }
    return day;
  });
  loading.value = false;
}

function toggleBreak(index: number) {
  const day = daysOfWeek.value[index];
  day.hasBreak = !day.hasBreak;
  if (day.hasBreak) {
    day.endTime1 = "12:00";
  } else {
    day.endTime1 = "18:00";
  }
}

onMounted(() => {
  getSchedules();
});

watch(company, () => {
  getSchedules();
});
</script>
