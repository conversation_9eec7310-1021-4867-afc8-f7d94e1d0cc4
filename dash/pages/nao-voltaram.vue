<template>
  <div class="container-table w-full">
    <table-base
      :columns="columns"
      :rows="rows"
      title="Pacientes que não retornaram"
      no-create
      hide-actions
    >
      <template #items="{ value }">
        <td>
          <span v-for="(item, i) in value" :key="i">
            <div>{{ item.quantity }}x {{ item.product_name }}</div>
          </span>
        </td>
      </template>
    </table-base>
  </div>
</template>

<script setup lang="ts">
const rows = [
  {
    user_name: "<PERSON>a<PERSON><PERSON>",
    user_email: "<PERSON>ã<PERSON>@gmail.com",
    last_visit: new Date(),
  },
  {
    user_name: "<PERSON><PERSON><PERSON><PERSON>",
    user_email: "<PERSON>ã<PERSON>@gmail.com",
    last_visit: new Date(),
  },
  {
    user_name: "<PERSON><PERSON><PERSON><PERSON>",
    user_email: "<PERSON>ã<PERSON>@gmail.com",
    last_visit: new Date(),
  },
  {
    user_name: "<PERSON><PERSON><PERSON><PERSON>",
    user_email: "<PERSON>ã<PERSON>@gmail.com",
    last_visit: new Date(),
  },
];

const columns = [
  { label: "Nome", key: "user_name" },
  { label: "Email", key: "user_email" },
  { label: "Ultimo atendimento", type: "date", key: "last_visit" },
];
</script>
