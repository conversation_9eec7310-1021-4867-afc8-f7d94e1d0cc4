<template>
  <div class="container-table">
    <div
      class="h-auto space-y-10 overflow-auto"
      :cladivss="user.isWorker && ''"
    >
      <order-card
        :order="order"
        v-for="order in orders"
        :key="order.id"
      ></order-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useOrderStore } from "@/store/order";
import { useToast } from "vue-toast-notification";
import type { newOrder } from "~/models/newOrder";
import { useLoginStore } from "~/store/user";
const user = useLoginStore().userInfo;

const toast = useToast();
const orders = ref<newOrder[]>([]);
const orderStore = useOrderStore();

const data = [
  {
    id: 1,
    items: [
      {
        service: {
          id: "189238912",
          description: "",
          name: "<PERSON>abe<PERSON> e Barba",
          price: 300,
          duration: 120,
        },
        user: {
          id: "341897123",
          name: "<PERSON>",
        },
        hour: "23:35",
      },
    ],
    created_at: "2024-05-01 23:35",
  },
  {
    id: 2,
    items: [
      {
        service: {
          id: "1892384255912",
          description: "",
          name: "Cabelo e Barba",
          price: 300,
          duration: 120,
        },
        user: {
          id: "34184232697123",
          name: "Azeitona Ferreira",
        },
        hour: "23:35",
      },
    ],
    created_at: "2024-05-01 23:35",
  },
  {
    id: 3,
    items: [
      {
        service: {
          id: "18923842912",
          description: "",
          name: "Cabelo e Barba",
          price: 300,
          duration: 120,
        },
        user: {
          id: "34189237123",
          name: "Caio Ferreira",
        },
        hour: "23:35",
      },
    ],
    created_at: "2024-05-01 23:35",
  },
  {
    id: 4,
    items: [
      {
        service: {
          id: "1892389143212",
          description: "",
          name: "Cabelo e Barba",
          price: 300,
          duration: 120,
        },
        user: {
          id: "341897234123",
          name: "Robson Ferreira",
        },
        hour: "23:35",
      },
    ],
    created_at: "2024-05-01 23:35",
  },
];

async function getData() {
  try {
    orderStore.loading = true;
    orders.value = data;
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  } finally {
    orderStore.loading = false;
  }
}
onMounted(async () => {
  await getData();
});
</script>
