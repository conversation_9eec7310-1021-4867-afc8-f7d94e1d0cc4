<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base
          :totalItems="totalItems"
          :columns="columns"
          :loading="loading"
          :rows="services"
          v-model:per_page="per_page"
          v-model:page="page"
          class="flex-1"
          @new="
            () => {
              selectedService = undefined;
              isOpened = true;
            }
          "
          @edit="handleEdit"
          @delete="deleteServiceById"
        >
          <template #filter>
            <div class="flex gap-4 -mt-1 items-center">
              <input-base
                type="text"
                label="Buscar"
                label-style="!-mb-2 ml-2 z-10 text-xs"
                class="sm:min-w-52"
                v-model="filters.name"
              />
              <input-base
                :options="[
                  { value: 'name', label: 'Nome' },
                  { value: 'price', label: 'Preço' },
                  { value: 'duration', label: 'Duração' },
                ]"
                label-style="!-mb-2 ml-2 z-10 text-xs"
                class="hidden sm:flex"
                label="Ordenar por"
                v-model="filters.sort_by"
              />

              <input-base
                label-style="!-mb-2 ml-2 z-10 text-xs"
                class="hidden sm:flex"
                v-model="filters.direction"
                :options="[
                  { value: 'asc', label: 'Crescente' },
                  { value: 'desc', label: 'Decrescente' },
                ]"
                label="Ordem"
              />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button
                  @click="filterModalOpen = true"
                  size="sm"
                  class="mr-3 btn-circle"
                >
                  <AdjustmentsHorizontalIcon class="w-5" />
                </base-button>
              </div>
            </div>
          </template>

          <template #duration="{ value }">
            {{ handleTime(value) }}
          </template>
        </table-base>
      </div>
    </div>
    <base-dialog
      :title="`${selectedService ? 'Editar' : 'Adicionar'} Serviço`"
      v-model="isOpened"
    >
      <services-register-form
        v-if="isOpened"
        @submit="handleFormSubmit"
        :service="selectedService"
        :loading="loading"
      />
    </base-dialog>
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base
          type="text"
          label="Buscar"
          label-style="!-mb-2 ml-2 z-10 text-xs"
          class="sm:min-w-52"
          v-model="filters.name"
        />
        <input-base
          :options="[
            { value: 'name', label: 'Nome' },
            { value: 'price', label: 'Preço' },
            { value: 'duration', label: 'Duração' },
          ]"
          label-style="!-mb-2 ml-2 z-10 text-xs"
          class=""
          label="Ordenar por"
          v-model="filters.sort_by"
        />

        <input-base
          label-style="!-mb-2 ml-2 z-10 text-xs"
          class=""
          v-model="filters.direction"
          :options="[
            { value: 'asc', label: 'Crescente' },
            { value: 'desc', label: 'Decrescente' },
          ]"
          label="Ordem"
        />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { type Service } from "@/models/services";
import { useServiceStore } from "@/store/service";
import { ref, watch, onMounted } from "vue";
import { useToast } from "vue-toast-notification";
import { handleTime } from "@/utils/formatFunctions";
import { api } from "~/server/api";
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";
const filterModalOpen = ref();
const serviceStore = useServiceStore();
const services = ref<Service[]>([]);
const selectedService = ref<Service>();
const isOpened = ref(false);
const toast = useToast();
const loading = ref(false);
const per_page = ref(10);
const page = ref(1);
const filters = ref({
  name: "",
  sort_by: "name",
  direction: "asc",
});
const columns = [
  { label: "Nome", key: "name", sm: true },
  { label: "Duração", key: "duration" },
  { label: "Preço", key: "price", type: "currency" },
];
const totalItems = ref(0);
const debouncedFetch = () => useDelay(async () => await getData(), 500);

async function handleFormSubmit(service: Service) {
  try {
    loading.value = true;
    const newPrice = parseFloat(
      service.price.toString().replace(",", ".").replace("R$", "")
    );
    const formatService: Service = {
      ...service,
      price: Number(newPrice),
    };

    if (selectedService.value) {
      await serviceStore.edit(formatService);
      toast.success("Serviço atualizado com sucesso!");
    } else {
      await serviceStore.add(formatService);
      toast.success("Serviço criado com sucesso!");
    }
    selectedService.value = undefined;
    isOpened.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  } finally {
    loading.value = false;
  }
}

async function deleteServiceById(item: Service) {
  try {
    await serviceStore.delete(item.id);
    toast.warning("Serviço deletado!");
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  }
}

function handleEdit(service: Service) {
  selectedService.value = service;
  isOpened.value = true;
}

async function getData() {
  try {
    loading.value = true;
    const { data } = await api("/services", {
      params: {
        per_page: per_page.value,
        page: page.value,
        name: filters.value.name,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    services.value = data.data;
    totalItems.value = data.total;
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  } finally {
    loading.value = false;
  }
}
function handleFilter() {
  filterModalOpen.value = false;
  getData();
}
watch(
  [per_page, page, filters],
  ([, , filtersVal]) => {
    if (filterModalOpen.value) return;
    if (filtersVal.name) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

onMounted(async () => {
  await getData();
});
</script>
