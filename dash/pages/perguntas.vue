<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <table-base :total-items title="Perguntas" :columns="columns" hide-delete :rows="questions"
        v-model:per_page="per_page" v-model:page="page" @edit="handleEdit" @delete="deleteQuestion" @new="
          () => {
            isOpened = true;
            isEditing = false;
          }
        " :loading="loading">
        <template #filter>
          <div class="flex gap-4 items-center">
            <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
              v-model="filters.label" />
            <!-- <input-base
              label="Ordenar por"
              class="hidden sm:flex"
              label-style="!-mb-2 ml-2 z-10 text-xs"
              v-model="filters.sort_by"
              :options="[
                { value: 'ordem', label: 'Ordem' },
                { value: 'label', label: 'T<PERSON>tu<PERSON>' },
              ]"
            /> -->
            <input-base label="Ordem" class="hidden sm:flex" label-style="!-mb-2 ml-2 z-10 text-xs"
              v-model="filters.direction" :options="[
                { value: 'asc', label: 'Crescente' },
                { value: 'desc', label: 'Decrescente' },
              ]" />
            <div class="sm:hidden">
              <div class="text-xs -mt-1">Filtros</div>
              <base-button @click="filterModalOpen = true" size="sm" class="mr-3 btn-circle">
                <AdjustmentsHorizontalIcon class="w-5" />
              </base-button>
            </div>
          </div>
        </template>
        <template #isRequired="{ value }">
          <td>
            <div class="flex items-center justify-center">
              <span v-if="value" class="text-green-500">Sim</span>
              <span v-else class="text-red-500">Não</span>
            </div>
          </td>
        </template>
        <template #type="{ value }">
          <td>
            <div class="flex items-center justify-center">
              <span v-if="value === 'string'" class="">Texto curto</span>
              <span v-else-if="value === 'textarea'" class="">Texto longo</span>
              <span v-else-if="value === 'multiple'" class="">Múltipla escolha</span>
              <span v-else-if="value === 'date'" class="">Data</span>
              <span v-else-if="value === 'switch'" class="">Switch</span>
            </div>
          </td>
        </template>
        <template #created_at="{ value }">
          {{ dayjs(value).format("DD/MM/YYYY") }}
        </template>
      </table-base>
    </div>

    <base-dialog :title="selectedQuestion ? 'Editar pergunta' : 'Cadastrar nova pergunta'" v-model="isOpened"
      @close="resetFormValues">
      <form v-if="isOpened" @submit.prevent="onSubmit" class="p-4 sm:pb-14">
        <div class="space-y-4">
          <!-- <input-base
            type="number"
            label="Ordem *"
            v-model="ordem"
            :error="errors.ordem"
          /> -->
          <input-base type="text" label="Texto da pergunta *" v-model="label" :error="errors.label" />
          <div class="flex items-center gap-2">
            <label class="cursor-pointer">
              <span class="mr-2">Campo obrigatório?</span>
              <input type="checkbox" v-model="isRequired" class="toggle toggle-primary" />
            </label>
          </div>
          <input-base label="Tipo de campo *" v-model="type" :error="errors.type" :options="[
            { value: 'string', label: 'Texto curto' },
            { value: 'textarea', label: 'Texto longo' },
            { value: 'multiple', label: 'Múltipla escolha' },
            { value: 'date', label: 'Data' },
            { value: 'switch', label: 'Switch' },
          ]" />

          <div v-if="type === 'multiple'" class="space-y-3">
            <div class="flex items-center justify-between">
              <h3 class="font-medium">Opções</h3>
              <base-button size="sm" @click="addOption" type="button">
                Adicionar opção
              </base-button>
            </div>
            <div v-for="(option, index) in options" :key="index" class="flex gap-2">
              <input-base type="text" v-model="options[index]" :error="errors?.options?.[index]"
                placeholder="Digite a opção" />
              <base-button type="button" variant="error" size="sm" @click="removeOption(index)">
                Remover
              </base-button>
            </div>
          </div>
        </div>
        <div class="fixed bottom-2 md:bottom-4 left-0 px-4 md:px-10 w-full">
          <base-button type="submit" class="w-full mt-4" :loading="loading">
            {{ selectedQuestion ? "Salvar pergunta" : "Criar pergunta" }}
          </base-button>
        </div>
      </form>
    </base-dialog>

    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
          v-model="filters.label" />
        <input-base label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sort_by" :options="[
          { value: 'ordem', label: 'Ordem' },
          { value: 'label', label: 'Título' },
        ]" />
        <input-base label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" :options="[
          { value: 'asc', label: 'Crescente' },
          { value: 'desc', label: 'Decrescente' },
        ]" />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";
import dayjs from "dayjs";
import { useToast } from "vue-toast-notification";
import { useForm } from "vee-validate";
import * as yup from "yup";
import { api } from "~/server/api";

const selectedQuestion = ref();

const schema = yup.object({
  label: yup.string().required("Título da pergunta é obrigatório"),
  type: yup.string().required("Tipo de campo é obrigatório"),
  isRequired: yup.boolean(),
  options: yup.array().when("type", {
    is: "multiple",
    then: (schema) =>
      schema
        .min(1, "Adicione pelo menos uma opção")
        .of(yup.string().required("Opção é obrigatória")),
    otherwise: (schema) => schema,
  }),
});

const { handleSubmit, resetForm, defineField, errors, setValues } = useForm({
  validationSchema: schema,
  initialValues: {
    label: "",
    isRequired: false,
    type: "string",
    options: [],
  },
  validateOnMount: false,
});

const isEditing = ref(false);
const [label] = defineField("label");
const [isRequired] = defineField("isRequired");
const [type] = defineField("type");
const [options] = defineField("options");
const filterModalOpen = ref(false);
const per_page = ref(10);
const page = ref(1);
const toast = useToast();
const isOpened = ref(false);
const questions = ref([]);
const loading = ref(false);
const totalItems = ref(0);

const filters = ref({
  label: "",
  sort_by: "ordem",
  direction: "asc",
});

const columns = [
  // { label: "Ordem", key: "ordem", sm: true },
  { label: "Título", key: "label", sm: true },
  { label: "Tipo", key: "type" },
  { label: "Obrigatório", key: "isRequired" },
  // { label: "Possui resposta anterior", key: "hasPrevious" },
];

function resetFormValues() {
  selectedQuestion.value = undefined;
  resetForm();
}

const onSubmit = handleSubmit(async (values) => {
  try {
    loading.value = true;
    if (selectedQuestion.value) {
      await api.put(`/questions/${selectedQuestion.value.id}`, values);
      toast.success("Pergunta atualizada com sucesso!");
    } else {
      // The backend will automatically associate the question with the current professional
      // and update the hasQuestionary field
      await api.post("/questions", values);
      toast.success("Pergunta cadastrada com sucesso!");
    }
    isOpened.value = false;
    resetFormValues();
    await getQuestions();
  } catch (err) {
    console.error(err);
    toast.error(
      selectedQuestion.value
        ? "Erro ao atualizar pergunta!"
        : "Erro ao cadastrar pergunta!"
    );
  } finally {
    loading.value = false;
  }
});

function handleFilter() {
  filterModalOpen.value = false;
  getQuestions();
}

async function getQuestions() {
  try {
    loading.value = true;
    // Use the my-questions endpoint which automatically filters by the current professional
    const { data } = await api("/my-questions", {
      params: {
        label: filters.value.label,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    questions.value = data;
    totalItems.value = data.length;
  } catch (error) {
    toast.error("Erro ao carregar perguntas!");
  } finally {
    loading.value = false;
  }
}

function handleEdit(question) {
  selectedQuestion.value = question;
  setValues({
    label: question.label,
    isRequired: question.isRequired,
    type: question.type,
    options: question.options || [],
  });
  isEditing.value = true;
  isOpened.value = true;
}

async function deleteQuestion(question) {
  try {
    loading.value = true;
    // The backend will automatically update the hasQuestionary field
    // if this is the last question for the professional
    await api.delete(`/questions/${question.id}`);
    toast.warning("Pergunta deletada!");
    await getQuestions();
  } catch (error) {
    toast.error("Erro ao deletar pergunta!");
  } finally {
    loading.value = false;
  }
}

const debouncedFetch = () => useDelay(async () => await getQuestions(), 500);

watch(
  filters,
  () => {
    if (filterModalOpen.value) return;
    if (filters.value.label) {
      debouncedFetch();
    } else {
      getQuestions();
    }
  },
  { deep: true }
);

watch(isOpened, () => {
  if (!isOpened.value) {
    resetFormValues();
  }
});

function addOption() {
  options.value.push("");
}

function removeOption(index: number) {
  options.value.splice(index, 1);
}

onMounted(getQuestions);
</script>
