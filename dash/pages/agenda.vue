<template>
  <div>
    <ClientOnly>
      <div class="container-table px-1 mx-0 sm:px-2 sm:mx-2 overflow-auto relative">
        <div class="flex h-full">
          <ScheduleXCalendar v-if="showCalendar" :calendar-app="calendarApp" class="flex-1">
            <template #headerContent>
              <div v-if="currentWidth < 600" class="w-full">
                <div class="flex mb-2">
                  <div :class="currentWidth > 400 ? 'mx-auto text-lg' : 'mr-auto'" class="flex">
                    <button @click="setDate(false)" tabindex="0" class="sx__chevron-wrapper sx__ripple">
                      <i class="sx__chevron sx__chevron--previous" />
                    </button>
                    <div :class="{ 'mx-auto': currentWidth > 400 }">
                      <div>
                        {{ currentDate }}
                      </div>
                      <div class="-mt-1">
                        {{ currentYear }}
                      </div>
                    </div>
                    <button @click="setDate(true)" tabindex="0" class="sx__chevron-wrapper sx__ripple">
                      <i class="sx__chevron sx__chevron--next" />
                    </button>
                  </div>
                  <div :class="{ hidden: currentWidth > 400 }" class="dropdown dropdown-bottom dropdown-end">
                    <div tabindex="0" role="button" class="btn rounded-full btn-primary">
                      Cadastrar
                    </div>
                    <ul tabindex="0"
                      class="dropdown-content border shadow-lg divide-y-2 menu bg-base-100 z-50 w-52 p-2">
                      <li li @click="handleNewAppointment">
                        <a class="h-12 flex items-center">Novo agendamento</a>
                      </li>
                      <li @click="isOpenedBlock = true">
                        <a class="h-12 flex items-center">Horário bloqueado</a>
                      </li>
                    </ul>
                  </div>
                  <div class="flex gap-2">
                    <div @click="handleNewAppointment" class="btn btn-rounded rounded-full btn-primary"
                      :class="{ hidden: currentWidth <= 400 }">
                      {{ currentWidth <= 500 ? "Agendar" : "Novo agendamento" }} </div>
                        <div :class="{ hidden: currentWidth <= 400 }" @click="isOpenedBlock = true"
                          class="btn rounded-full">
                          Bloquear
                          {{ currentWidth >= 500 ? "horário" : "" }}
                        </div>
                    </div>
                  </div>
                  <div class="grid grid-cols-12 gap-1">
                    <SxDatePicker v-model="date" class="" :class="currentWidth > 400 ? 'col-span-7' : 'col-span-6'"
                      :config="{ locale: 'pt-BR' }" />
                    <input-base hide-label class="h-full" :class="currentWidth > 400 ? 'col-span-5' : 'col-span-6'"
                      input-classes="!h-full rounded-sm" v-model="viewType" :options="[
                        { label: 'Dia', value: 'day' },
                        { label: 'Semana', value: 'week' },
                        { label: 'Mês', value: 'month-agenda' },
                      ]" option-label="label" option-value="value" />
                  </div>
                </div>
                <template v-else>
                  <div class="sx__calendar-header-content">
                    <button class="sx__today-button sx__ripple hidden lg:block"
                      @click="date = dayjs().format('YYYY-MM-DD')">
                      Hoje
                    </button>
                    <div class="flex">
                      <button @click="setDate(false)" tabindex="0" class="sx__chevron-wrapper sx__ripple btn-xs">
                        <i class="sx__chevron sx__chevron--previous" /></button><button @click="setDate(true)"
                        tabindex="0" class="sx__chevron-wrapper sx__ripple">
                        <i class="sx__chevron sx__chevron--next" />
                      </button>
                    </div>
                    <span class="sx__range-heading">{{ currentDate }} {{ currentYear }}</span>
                  </div>
                  <div class="flex sm:gap-2 lg:gap-4">
                    <div class="grid grid-cols-12 gap-4">
                      <input-base hide-label class="col-span-5 md:col-span-5 h-full" input-classes="!h-full rounded-sm"
                        v-model="viewType" :options="[
                          { label: 'Dia', value: 'day' },
                          { label: 'Semana', value: 'week' },
                          { label: 'Mês', value: 'month-grid' },
                        ]" option-label="label" option-value="value" />
                      <SxDatePicker v-model="date" class="col-span-7" :config="{ locale: 'pt-BR' }" />
                    </div>
                    <div @click="handleNewAppointment" class="btn btn-primary rounded-full bg-primary" :class="{
                      'ml-2': currentWidth >= 600 && currentWidth < 640,
                    }">
                      {{ currentWidth >= 1150 ? "Novo agendamento" : "Agendar" }}
                    </div>
                    <div @click="isOpenedBlock = true" class="btn rounded-full">
                      Bloquear
                      {{ currentWidth >= 1150 ? "horário" : "" }}
                    </div>
                  </div>
                </template>
            </template>
            <template #eventModal="{ calendarEvent }">
              <ScheduleEvent @finished="handleFinished" v-bind="calendarEvent" />
            </template>
            <template #timeGridEvent="{ calendarEvent }">
              <div class="w-full h-full sx__day-off" v-if="calendarEvent.isBlocked" style="
                  cursor: not-allowed;
                  pointer-events: none;
                  background-color: #ddd;
                "></div>
              <div v-else class="bg-primary h-full border-b-4 rounded-none border-gray-500  w-full p-2 text-white">
                <div>
                  <div class="flex items-center">
                    <div class="text-sm -mb-1 mr-3">
                      {{ calendarEvent.title }}
                    </div>
                    <div class="font-bold">
                      {{
                        `${dayjs(calendarEvent.start).format("HH:mm")} - ${dayjs(calendarEvent.end).format("HH:mm")}`
                      }}
                    </div>
                  </div>
                  <div>
                    {{ calendarEvent.client_name }}
                  </div>
                </div>
              </div>
            </template>
          </ScheduleXCalendar>
        </div>
        <div v-if="!showCalendar" class="absolute top-2/4 -mt-10 -ml-5 left-2/4 flex justify-center items-center">
          <div class="loading loading-spinner loading-lg loading-primary"></div>
        </div>
      </div>
      <base-dialog :title="`${selectedAppointment?.service_id ? 'Editar' : 'Novo'} agendamento`" v-model="isOpened">
        <schedule-register-form v-if="isOpened" @submit="handleFormSubmit" :appointment="selectedAppointment" :services
          :professionals :clients :startDate :loading />
      </base-dialog>
      <base-dialog title="Bloquear horário" v-model="isOpenedBlock">
        <schedule-block-time v-if="isOpenedBlock" @submit="handleBlock" :date :loading />
      </base-dialog>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import {
  CalendarApp,
  createCalendar,
  createViewDay,
  createViewMonthAgenda,
  createViewMonthGrid,
  createViewWeek,
} from "@schedule-x/calendar";
import { createCalendarControlsPlugin } from "@schedule-x/calendar-controls";
import { createCurrentTimePlugin } from "@schedule-x/current-time";
import { createEventModalPlugin } from "@schedule-x/event-modal";
import { createEventsServicePlugin } from "@schedule-x/events-service";
import "@schedule-x/theme-default/dist/index.css";
import { ScheduleXCalendar, SxDatePicker } from "@schedule-x/vue";
import dayjs, { type ManipulateType } from "dayjs";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";
import type { Event } from "~/utils/models";
const date = ref(dayjs().format("YYYY-MM-DD"));
const viewType = ref("day");
const clients = ref([]);
const loading = ref();
const professionals = ref([]);
const services = ref([]);
const selectedAppointment = ref();
const calendarControls = createCalendarControlsPlugin();
const eventsServicePlugin = createEventsServicePlugin();
const eventModal = createEventModalPlugin();
const currentTime = createCurrentTimePlugin();
const manualUpdate = ref();
const appointments = ref();
const startDate = ref();
const isOpened = ref(false);
const isOpenedBlock = ref(false);
let calendarApp: CalendarApp;
const showCalendar = ref(false);
function setDate(add = false) {
  const currentDate = calendarControls.getDate();
  const formatedDate = dayjs(currentDate);
  const period = calendarControls.getView().split("-")[0] as ManipulateType;
  const finalDate = add
    ? formatedDate.add(1, period).format("YYYY-MM-DD")
    : formatedDate.subtract(1, period).format("YYYY-MM-DD");
  calendarControls.setDate(finalDate);
  date.value = finalDate;
  manualUpdate.value = true;
}
const currentDate = computed(() => {
  var objDate = new Date();
  objDate.setDate(1);
  objDate.setMonth(dayjs(date.value).month());
  const month = objDate.toLocaleString("pt-br", { month: "long" });
  const capitalizedMonth =
    String(month).charAt(0).toUpperCase() + String(month).slice(1);
  return `${capitalizedMonth}`;
});
const currentYear = computed(() => {
  return `${dayjs(date.value).year()}`;
});
function formatAppointments(events: Event[]) {
  const blocked = [];
  const eventsList = [];
  events.forEach((event) => {
    const obj = {
      id: event.id,
      title: `${event.title}`,
      start: dayjs(event.start_date).format("YYYY-MM-DD HH:mm"),
      end: dayjs(event.end_date).format("YYYY-MM-DD HH:mm"),
      service_id: event.service_id,
      client_id: event.company_user_id,
      client_name: event.client_name,
      professional_id: event.professional_id,
    };
    if (event.status === "blocked") {
      blocked.push(obj);
    } else {
      eventsList.push(obj);
    }
  });
  return [eventsList, blocked];
}

const startHour = ref();
async function getAppointments() {
  const { data } = await api("my-schedule");
  const [events, blocked] = formatAppointments(data);
  let gridHeight
  const alturaDispositivo = window.innerHeight;
  if(alturaDispositivo < 650){
    gridHeight = 700
  }
  else if(alturaDispositivo < 700){
    gridHeight = 800
  }
  else if(alturaDispositivo < 750){
    gridHeight = 900
  }
  else if(alturaDispositivo < 800){
    gridHeight = 1000
  }
  else if(alturaDispositivo < 850){
    gridHeight = 1100
  }
  else {
    gridHeight = 1200
  }
  appointments.value = events;
  calendarApp = createCalendar(
    {
      locale: "pt-BR",
      selectedDate: dayjs().format("YYYY-MM-DD"),
      defaultView: "day",
      dayBoundaries: {
        start: minDate.value,
        end: maxDate.value,
      },
      isDark: isDark.value,
      weekOptions: {
        gridHeight,
      },
      views: [
        createViewDay(),
        createViewWeek(),
        createViewMonthGrid(),
        createViewMonthAgenda(),
      ],
      events: appointments.value,
      callbacks: {
        onClickDateTime(dateTime) {
          startDate.value = dateTime; // e.g. 2024-01-01 12:37
          startHour.value = dayjs(dateTime).format("HH:mm");
          isOpened.value = true;
        },
        onClickAgendaDate(date) {
          calendarControls.setDate(date);
          calendarControls.setView("day");
          viewType.value = "day";
        },

        onDoubleClickEvent({ id }) {
          const [event] = appointments.value.filter(
            (appointment: { id: number }) => appointment.id == id
          );
          selectedAppointment.value = event;
          isOpened.value = true;
        },

        onRangeUpdate() {
          const view = calendarControls.getView();
          if (view === "day") {
            resetStyles();
          } else setStyleDayOff();
        },
        onRender() {
          createBlockedTimes();
        },
      },
    },
    [eventsServicePlugin, calendarControls, eventModal, currentTime]
  );
  blocked.forEach((event) => {
    handleBlocked(event);
  });
  showCalendar.value = true;
  return data;
}
async function handleBlocked(event) {
  eventsServicePlugin.add({
    title: "Bloqueio",
    start: dayjs(event.start).format("YYYY-MM-DD HH:mm"),
    end: dayjs(event.end).format("YYYY-MM-DD HH:mm"),
    id: event.id,
    isBlocked: true,
    isRecurrent: false,
  });
}
async function getServices() {
  const { data } = await api("services", {
    params: { per_page: 999, page: 1 },
  });
  services.value = data.data;
}
async function getProfessionals() {
  const { data } = await api("/professionals");
  return data.data;
}
async function getClients() {
  const { data } = await api("/clients");
  const users = data.data.map((client: { user: User; id: number }) => {
    return { ...client.user, clientId: client.id };
  });
  return users;
}
async function getData() {
  await Promise.all([getAppointments()]);
  const [allClients, allProfessionals] = await Promise.all([
    getClients(),
    getProfessionals(),
    getServices(),
  ]);
  professionals.value = allProfessionals;
  clients.value = allClients;
}
function handleNewAppointment() {
  startHour.value = undefined;
  startDate.value = date.value;
  selectedAppointment.value = {};
  isOpened.value = true;
}

async function handleBlock(e) {
  loading.value = true;
  const startTime = e.startHour.slice(0, 2) + ":" + e.startHour.slice(2);
  const endTime = e.endHour.slice(0, 2) + ":" + e.endHour.slice(2);
  try {
    const { data } = await api.post("/block-time", {
      date: e.date,
      start_time: startTime,
      end_time: endTime,
    });

    eventsServicePlugin.add({
      title: "Bloqueio",
      start: dayjs(data.start_date).format("YYYY-MM-DD HH:mm"),
      end: dayjs(data.end_date).format("YYYY-MM-DD HH:mm"),
      id: data.id,
      isBlocked: true,
      isRecurrent: false,
    });

    useToast().success("Horário bloqueado");
    isOpenedBlock.value = false;
  } catch (error) {
    useToast().error("Erro ao bloquear horário");
  } finally {
    loading.value = false;
  }
}

async function handleFormSubmit(e) {
  loading.value = true;
  try {
    const { data } = await api.post("/orders", {
      company: useCompanyStore().slug,
      services: [
        {
          userId: e.client,
          service: { id: e.service },
          date: e.date,
          hour: e.hour,
          variant_id: e.variant_id,
        },
      ],
    });
    const appointment = data.order.appointments[0];
    const service = data.order.appointments[0].service;
    eventsServicePlugin.add({
      title: service?.name,
      start: dayjs(data.order.appointments[0].start_date).format(
        "YYYY-MM-DD HH:mm"
      ),
      end: dayjs(data.order.appointments[0].end_date).format(
        "YYYY-MM-DD HH:mm"
      ),
      id: appointment.id,
      client_id: e.client,
      service_id: service.id,
      client_name: appointment.client_name,
      professional_id: appointment.company_professional_id,
    });
    useToast().success("Agendamento criado");
    isOpened.value = false;
  } catch {
  } finally {
    loading.value = false;
  }
}
function createBlockedTimes() {
  for (let i = 0; i < 60; i++) {
    const today = dayjs();
    const day = today.add(i, "day");
    // remove accents
    const dayOfWeek = day.format("dddd").split("-")[0].replace("á", "a");
    const key = daysOfWeek.value.filter((day) => day.name === dayOfWeek)[0]
      ?.pauseKey;
    const blockedTime = company[key];
    if (blockedTime) {
      const [startTime, endTime] = blockedTime.split("-");
      const blockedTimeEvent = {
        title: "Bloqueio de horário",
        start: day.format("YYYY-MM-DD") + " " + startTime,
        end: day.format("YYYY-MM-DD") + " " + endTime,
        isBlocked: true,
        isRecurrent: true,
        id: day.format("YYYY-MM-DD") + " " + startTime,
      };
      eventsServicePlugin.add(blockedTimeEvent);
    }
  }
}
const setStyleDayOff = () => {
  const classesPerDay = {
    segunda: "sx__monday",
    terça: "sx__tuesday",
    quarta: "sx__wednesday",
    quinta: "sx__thursday",
    sexta: "sx__friday",
    sabado: "sx__saturday",
    domingo: "sx__sunday",
  };
  const dates = daysOfWeek.value
    .map((day) => {
      const time = company[day.key];
      if (!time) {
        return day.name;
      } else return null;
    })
    .filter((x) => !!x);
  dates.forEach((date) => {
    const styleSheet = document.styleSheets[0];

    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__time-grid-day {
        cursor: not-allowed;
        pointer-events: none;
        background-color: #ddd;
      }
      `,
      styleSheet.cssRules.length
    );
    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__month-grid-day {
        cursor: not-allowed;
        pointer-events: none;
        background-color: #ddd;
      }
      `,
      styleSheet.cssRules.length
    );
    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__month-agenda-day {
        // cursor: not-allowed;
        // pointer-events: none;
        background-color: #ddd;
        border-radius: 50%;
      }
      `,
      styleSheet.cssRules.length
    );
    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__month-agenda-day > div {
        margin-left: -3px;
        margin-top: 3px;
      }
      `,
      styleSheet.cssRules.length
    );
  });
};
const resetStyles = () => {
  const classesPerDay = {
    segunda: "sx__monday",
    terça: "sx__tuesday",
    quarta: "sx__wednesday",
    quinta: "sx__thursday",
    sexta: "sx__friday",
    sabado: "sx__saturday",
    domingo: "sx__sunday",
  };
  const dates = daysOfWeek.value
    .map((day) => {
      const time = company[day.key];
      if (!time) {
        return day.name;
      } else return null;
    })
    .filter((x) => !!x);
  dates.forEach((date) => {
    const styleSheet = document.styleSheets[0];
    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__week-grid__date {
        display: flex !important
      }
      `,
      styleSheet.cssRules.length
    );
    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__time-grid-day {
        display: flex !important
      }
      `,
      styleSheet.cssRules.length
    );
    styleSheet.insertRule(
      `.${classesPerDay[date as keyof typeof classesPerDay]}.sx__month-grid-day {
        display: flex
      }
      `,
      styleSheet.cssRules.length
    );
  });
};
const currentWidth = ref();
function getWidth() {
  currentWidth.value = window.innerWidth;
}
const colorMode = useColorMode();

const daysOfWeek = ref([
  {
    name: "segunda",
    key: "monday_time",
    pauseKey: "monday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
  {
    name: "terça",
    key: "tuesday_time",
    pauseKey: "tuesday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
  {
    name: "quarta",
    key: "wednesday_time",
    pauseKey: "wednesday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
  {
    name: "quinta",
    key: "thursday_time",
    pauseKey: "thursday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
  {
    name: "sexta",
    key: "friday_time",
    pauseKey: "friday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
  {
    name: "sabado",
    key: "saturday_time",
    pauseKey: "saturday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
  {
    name: "domingo",
    key: "sunday_time",
    pauseKey: "sunday_pause",
    isActive: false,
    startTime: "",
    endTime: "",
  },
]);
const company = useCompanyStore();
const isDark = computed(() => colorMode.preference !== "light");
onMounted(() => {
  getMinAndMaxTimes();
  getData();
  window.addEventListener("resize", getWidth);
  getWidth();
});
watch(isDark, (val) => {
  setTimeout(() => {
    const wrapper = document.querySelector(".sx__calendar-wrapper");
    if (!wrapper) return;
    if (val) {
      wrapper.classList.add("is-dark");
    } else {
      wrapper.classList.remove("is-dark");
    }
  });
});
watch(viewType, (val) => {
  calendarControls.setView(val);
});
watch(date, (val) => {
  if (val !== calendarControls.getDate()) calendarControls.setDate(val);
});
const minDate = ref();
const maxDate = ref();
function getMinAndMaxTimes() {
  daysOfWeek.value.forEach((day) => {
    const timeRange = company[day.key];
    if (timeRange) {
      const [startTime, endTime] = timeRange.split("-");
      day.startTime = startTime;
      day.endTime = endTime;
      day.isActive = true;
      if (!minDate.value || startTime < minDate.value) {
        minDate.value = startTime;
      }
      if (!maxDate.value || endTime > maxDate.value) {
        maxDate.value = endTime;
      }
    }
  });
  if (!minDate.value) minDate.value = "00:00";
  if (!maxDate.value) maxDate.value = "23:59";
}
function handleFinished(id: number) {
  eventsServicePlugin.remove(id);
  useToast().success("Agendamento finalizado");
}
</script>

<style lang="scss">
// .sx__forward-backward-navigation {
//   .sx__chevron-wrapper:nth-child(2) {
//     display: none;
//   }
// }
.sx__event-modal {
  overflow: auto;
}

.sx__time-grid-event-inner {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sx__day-off {
  cursor: not-allowed;
  background-color: #f7f7f7;
  pointer-events: none;
}

@media (max-width: 400px) {
  .sx__date-picker-popup {
    width: 18.5rem !important;
  }

  .sx__week-grid {
    padding-left: 50px !important;
  }

  .sx__week-grid__date {
    margin-left: -40px;
  }

  .sx__week-grid__time-axis {
    width: calc(100% - 40px);
  }

  .sx__week-grid__hour-text {
    left: -25px;
  }

  .sx__calendar-header {
    padding-top: 10px;
    padding-bottom: 0px;
  }
}

.is-dark {
  .sx__calendar {
    background-color: oklch(var(--b1));
  }

  .sx__week-header {
    background-color: oklch(var(--b1));
  }

  .sx__date-input {
    background-color: oklch(var(--b1));
  }

  .sx__date-picker-popup {
    background-color: oklch(var(--b2));
  }
}

@media (max-width: 700px) {
  .sx__calendar-header {
    padding: 16px 4px;
  }
}

.sx__current-time-indicator {
  z-index: 5;
}

.sx__event-modal {
  z-index: 10;
}

.sx__date-picker-wrapper {
  width: 100%;
}

.sx__time-grid-event {
  border-radius: 0px !important;
}
</style>
