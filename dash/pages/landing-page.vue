<template>
  <div class="">
    <div
      class="container-table sm:px-1 sm:mx-1 md:px-2 md:mx-2 px-0 mx-0 overflow-auto"
    >
      <div class="flex flex-col items-center md:items-start md:flex-row w-full">
        <div></div>
        <MockPhone
          :buttonsColor
          :contrastText
          :backgroundColor
          :image
          :cardsColor
        >
          <div class="mt-1"></div>
        </MockPhone>
      </div>

      <base-button
        :loading
        @click="updateProfile"
        class="mt-auto"
      >
        Atualizar Dados
      </base-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PencilSquareIcon } from "@heroicons/vue/24/solid";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";
const services = [
  {
    name: "Corte",
    price: "30",
    duration: "30",
  },
];
const toast = useToast();
const loading = ref(false);
const buttonsColor = ref("");
const backgroundColor = ref("");
const cardsColor = ref("");
const image = ref(
  "https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg"
);
const newImage = ref();
async function updateProfile() {
  loading.value = true;
  try {
    const payload = {
      buttons_color: buttonsColor.value,
      background_color: backgroundColor.value,
      cards_color: cardsColor.value,
    };
    await api.put("/my-company", payload);
    loading.value = false;
    toast.success("Perfil Atualizado");
  } finally {
    loading.value = false;
  }
}
async function uploadImage(e: Event) {
  const target = e.currentTarget as HTMLInputElement;
  if (!target.files?.length) return;
  const [file] = target.files;
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = async () => {
    image.value = reader.result as string;
    await api.post("/update-logo", { image: reader.result });
  };
  newImage.value = file;
}
async function getProfile() {
  const company = useCompanyStore();
  backgroundColor.value = company.background_color;
  buttonsColor.value = company.buttons_color;
  cardsColor.value = company.cards_color;
  image.value = company.logo;
}
onMounted(() => {
  getProfile();
});

function getContrastColor(hex: string): number {
  hex = hex.replace("#", "");
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
  return luminance > 130 ? 0 : 1;
}

const contrastText = computed(() => getContrastColor(cardsColor.value));
</script>
