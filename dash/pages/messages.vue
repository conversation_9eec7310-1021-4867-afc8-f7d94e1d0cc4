<template>
  <div class="container mx-auto p-4">
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title text-2xl mb-6">Mensagens</h2>

        <!-- Filters -->
        <div class="bg-base-200 p-4 rounded-lg mb-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="form-control w-full">
              <label class="label">
                <span class="label-text">Número de Origem</span>
              </label>
              <input
                v-model="filters.senderNumber"
                type="text"
                placeholder="Número de origem"
                class="input input-bordered w-full"
                data-maska="(##) #####-####"
              />
            </div>

            <div class="form-control w-full">
              <label class="label">
                <span class="label-text">Número de Destino</span>
              </label>
              <input
                v-model="filters.receiverNumber"
                type="text"
                placeholder="Número de destino"
                class="input input-bordered w-full"
                data-maska="(##) #####-####"
              />
            </div>

            <div class="form-control w-full">
              <label class="label">
                <span class="label-text">Projeto</span>
              </label>
              <input
                v-model="filters.projectName"
                type="text"
                placeholder="Nome do projeto"
                class="input input-bordered w-full"
              />
            </div>

            <div class="form-control w-full">
              <label class="label">
                <span class="label-text">Data Inicial</span>
              </label>
              <input
                v-model="filters.startDate"
                type="date"
                class="input input-bordered w-full"
              />
            </div>

            <div class="form-control w-full">
              <label class="label">
                <span class="label-text">Data Final</span>
              </label>
              <input
                v-model="filters.endDate"
                type="date"
                class="input input-bordered w-full"
              />
            </div>

            <div class="flex items-end mb-2">
              <button
                @click="fetchMessages(1)"
                class="btn btn-primary w-full"
                :class="{ 'loading': loading }"
                :disabled="loading"
              >
                Filtrar
              </button>
            </div>
          </div>
        </div>

        <!-- Messages List Component -->
        <message-list
          :messages="messages"
          :loading="loading"
          :current-page="currentPage"
          :total-pages="totalPages"
          :total-messages="totalMessages"
          @view="viewMessageDetails"
          @page-change="fetchMessages"
        />
      </div>
    </div>

    <!-- Message Details Modal -->
    <dialog ref="messageModal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Detalhes da Mensagem</h3>

        <div v-if="selectedMessage" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="font-semibold">Data:</div>
              <div>{{ formatDate(selectedMessage.date) }}</div>
            </div>
            <div>
              <div class="font-semibold">Projeto:</div>
              <div>{{ selectedMessage.projectName }}</div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="font-semibold">De:</div>
              <div>{{ selectedMessage.senderName }}</div>
              <div>{{ formatPhone(selectedMessage.senderNumber) }}</div>
            </div>
            <div>
              <div class="font-semibold">Para:</div>
              <div>{{ selectedMessage.receiverName }}</div>
              <div>{{ formatPhone(selectedMessage.receiverNumber) }}</div>
            </div>
          </div>

          <div>
            <div class="font-semibold">Mensagem:</div>
            <div class="bg-base-200 p-3 rounded-lg mt-1 whitespace-pre-wrap">
              {{ selectedMessage.textMessage }}
            </div>
          </div>
        </div>

        <div class="modal-action">
          <form method="dialog">
            <button class="btn">Fechar</button>
          </form>
        </div>
      </div>
      <form method="dialog" class="modal-backdrop">
        <button>close</button>
      </form>
    </dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useToast } from 'vue-toast-notification';
import { api } from '~/server/api';
import { useLoginStore } from '~/store/user';
import MessageList from '~/components/messages/MessageList.vue';

const toast = useToast();

// Messages state
interface Message {
  id: number;
  senderName: string;
  senderNumber: string;
  receiverName: string;
  receiverNumber: string;
  textMessage: string;
  projectName: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

const messages = ref<Message[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const totalMessages = ref(0);
const messageModal = ref<HTMLDialogElement | null>(null);
const selectedMessage = ref<Message | null>(null);

// Filters
const filters = reactive({
  senderNumber: '',
  receiverNumber: '',
  projectName: '',
  startDate: '',
  endDate: '',
});

// Fetch messages with pagination and filters
async function fetchMessages(page: number) {
  if (page < 1) return;

  try {
    loading.value = true;
    currentPage.value = page;

    // Format phone numbers (remove non-numeric characters)
    const formattedSenderNumber = filters.senderNumber ? filters.senderNumber.replace(/\D/g, '') : '';
    const formattedReceiverNumber = filters.receiverNumber ? filters.receiverNumber.replace(/\D/g, '') : '';

    console.log("Fetching messages with filters:", {
      page,
      senderNumber: formattedSenderNumber,
      receiverNumber: formattedReceiverNumber,
      projectName: filters.projectName,
      startDate: filters.startDate,
      endDate: filters.endDate
    });

    // Get the token from the login store
    const loginStore = useLoginStore();
    const token = loginStore.token;

    if (!token) {
      toast.error('Você precisa estar logado para visualizar mensagens.');
      return;
    }

    // Prepare query parameters
    const params = {
      page,
      limit: 10,
      ...(formattedSenderNumber && { senderNumber: formattedSenderNumber }),
      ...(formattedReceiverNumber && { receiverNumber: formattedReceiverNumber }),
      ...(filters.projectName && { projectName: filters.projectName }),
      ...(filters.startDate && { startDate: filters.startDate }),
      ...(filters.endDate && { endDate: filters.endDate }),
    };

    // Call the API with the correct endpoint
    const response = await api.get('/messages', {
      params,
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // Update state with response data
    // Handle both Lucid paginated response format and regular array response
    if (response.data.data && response.data.meta) {
      // Lucid paginated response
      messages.value = response.data.data;
      totalPages.value = response.data.meta.last_page;
      totalMessages.value = response.data.meta.total;
    } else if (Array.isArray(response.data)) {
      // Regular array response
      messages.value = response.data;
      totalPages.value = 1;
      totalMessages.value = response.data.length;
    } else {
      // Fallback for unexpected response format
      messages.value = [];
      totalPages.value = 1;
      totalMessages.value = 0;
      console.error('Unexpected response format:', response.data);
    }

  } catch (error) {
    console.error('Error fetching messages:', error);
    toast.error('Erro ao carregar mensagens. Tente novamente.');
  } finally {
    loading.value = false;
  }
}

// View message details
function viewMessageDetails(message: Message) {
  selectedMessage.value = message;
  messageModal.value?.showModal();
}

// Format date
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

// Format phone number
function formatPhone(phone: string) {
  if (!phone) return '';

  // Format as (XX) XXXXX-XXXX
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
  }
  return phone;
}

// Load messages on component mount
onMounted(() => {
  fetchMessages(1);
});
</script>
