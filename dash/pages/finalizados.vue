<template>
  <div class="w-full">
    <div class="container-table" :class="user.isWorker && ''">
      <table-base
        :columns="columns"
        :rows="orders"
        hide-actions
        no-create
      >
        <template #items="{ value }">
          <td>
            <span v-for="(item, i) in value" :key="i">
              <div>{{ item.quantity }}x {{ item.product_name }}</div>
            </span>
          </td>
        </template>
      </table-base>
    </div>
    <loading :show="orderStore.loading" />
  </div>
</template>

<script setup lang="ts">
import { useOrderStore } from "@/store/order";
import { useToast } from "vue-toast-notification";
import { useLoginStore } from "~/store/user";
const user = useLoginStore().userInfo;

type Order = {
  id: number;
  total: string;
  user_email: string;
  items: object[];
};
const toast = useToast();
const orders = ref<Order[]>([]);
const orderStore = useOrderStore();

const columns = [
  { label: "Paciente", key: "user_email" },
  { label: "Items", key: "items" },
  { label: "Preço", key: "total", type: "currency" },
  { label: "Data", key: "updated_at", type: "date" },
];

async function getData() {
  try {
    orderStore.loading = true;
    const data = await orderStore.getFinisheds();
    orders.value = data;
  } catch (err) {
    console.error(err);
    toast.error("Error request!");
  } finally {
    orderStore.loading = false;
  }
}
onMounted(async () => {
  await getData();
});
</script>
