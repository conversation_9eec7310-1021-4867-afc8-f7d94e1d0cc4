<template>
  <div
    id="landing-page"
    class="flex bg-base-200 px-0 h-full min-w-full sm:px-5 flex-col"
  >
    <!-- Banner Section -->
    <div class="w-full h-[20vh] sm:h-[20vh] relative">
      <div class="carousel w-full h-full">
        <div
          v-for="(image, i) in bannerImages"
          :id="`slide-${i}`"
          :key="i"
          class="carousel-item relative w-full h-full"
        >
          <img :src="image" alt="Banner" class="w-full h-full object-cover" />

          <div
            v-if="bannerImages.length > 1"
            class="absolute left-5 right-5 top-1/2 flex -translate-y-1/2 transform justify-between"
          >
            <a :href="calcNavPrev(i)" class="btn btn-circle btn-xs">❮</a>
            <a :href="calcNavNext(i)" class="btn btn-circle btn-xs">❯</a>
          </div>
        </div>
      </div>

      <div
        class="absolute top-2/3 left-1/2 sm:left-24 transform -translate-x-1/2 w-32 h-32 border-4 border-white rounded-full overflow-hidden"
      >
        <nuxt-img :src="profileImage" class="w-full h-full object-cover" />
      </div>

      <!-- Nome do Estabelecimento com Desfoque -->
      <div
        class="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center"
      >
        <h1 class="text-4xl sm:text-5xl font-bold text-white drop-shadow-lg">
          {{ establishment.name }}
        </h1>
      </div>
    </div>

    <!-- Botões para alternar as seções com ícones -->
    <section class="pt-20 pb-6 md:py-5 px-5 bg-white shadow-md rounded-lg">
      <div class="flex justify-center space-x-4">
        <button
          @click="activeTab = 'contact'"
          :class="{
            'btn btn-neutral': activeTab === 'contact',
            'btn-outline': activeTab !== 'contact',
          }"
          class="py-2 px-4 rounded"
        >
          <i class="fas fa-map-marker-alt"></i>
        </button>
        <button
          @click="activeTab = 'about'"
          :class="{
            'btn btn-neutral': activeTab === 'about',
            'btn-outline': activeTab !== 'about',
          }"
          class="py-2 px-4 rounded"
        >
          <i class="fas fa-info-circle"></i>
        </button>
        <button
          @click="activeTab = 'social'"
          :class="{
            'btn btn-neutral': activeTab === 'social',
            'btn-outline': activeTab !== 'social',
          }"
          class="py-2 px-4 rounded"
        >
          <i class="fas fa-share-alt"></i>
        </button>
      </div>

      <!-- Seções -->
      <div
        v-if="activeTab === 'contact'"
        class="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-6"
      >
        <!-- Endereço -->
        <div
          v-if="establishment.address"
          class="flex items-center text-sm text-gray-700 md:justify-center"
        >
          <i class="fas fa-map-marker-alt mr-2 text-gray-600"></i>
          <div>
            <h3 class="font-medium">Endereço</h3>
            <p>{{ establishment.address }}</p>
          </div>
        </div>

        <!-- Telefone -->
        <div
          v-if="establishment.phone"
          class="flex items-center text-sm text-gray-700 md:justify-center"
        >
          <i class="fas fa-phone-alt mr-2 text-gray-600"></i>
          <div>
            <h3 class="font-medium">Telefone</h3>
            <p>{{ establishment.phone.join(', ') }}</p>
          </div>
        </div>

        <!-- Email -->
        <div
          v-if="establishment.email"
          class="flex items-center text-sm text-gray-700 md:justify-center"
        >
          <i class="fas fa-envelope mr-2 text-gray-600"></i>
          <div>
            <h3 class="font-medium">Email</h3>
            <p>{{ establishment.email }}</p>
          </div>
        </div>
      </div>

      <!-- Sobre o Estabelecimento -->
      <div v-if="activeTab === 'about'" class="mt-6 text-sm text-gray-700">
        <!-- Rating destacado -->
        <div v-if="establishment.rating" class="flex items-center mb-4">
          <i class="fas fa-star text-yellow-500 mr-2 text-xl"></i>
          <p class="font-medium text-lg">
            {{ establishment.rating.value }}
            <span class="text-sm text-gray-500">
              ({{ establishment.rating.total }} avaliações)
            </span>
          </p>
        </div>

        <!-- Descrição sobre o estabelecimento -->
        <p>
          {{ truncatedAbout }}
          <!-- Botão "Mostrar Mais" ou "Mostrar Menos" -->

          <button
            v-if="establishment.about.length > maxAboutLength"
            @click="toggleText"
            class="text-blue-500 mt-2 ml-1"
          >
            {{ showFullText ? 'Mostrar Menos' : 'Mostrar Mais' }}
          </button>
        </p>
      </div>

      <!-- Redes Sociais -->
      <div
        v-if="activeTab === 'social'"
        class="flex mt-6 text-center space-x-4 justify-center"
      >
        <!-- Facebook -->
        <div
          v-if="
            establishment.socialMedias && establishment.socialMedias.facebook
          "
        >
          <a
            :href="establishment.socialMedias.facebook"
            target="_blank"
            class="inline-block p-6 rounded-lg bg-blue-600 hover:bg-blue-500 text-white w-20 h-20 flex items-center justify-center"
          >
            <i class="fab fa-facebook-f text-2xl"></i>
          </a>
        </div>
        <!-- Instagram -->
        <div
          v-if="
            establishment.socialMedias && establishment.socialMedias.instagram
          "
        >
          <a
            :href="establishment.socialMedias.instagram"
            target="_blank"
            class="inline-block p-6 rounded-lg bg-pink-600 hover:bg-pink-500 text-white w-20 h-20 flex items-center justify-center"
          >
            <i class="fab fa-instagram text-2xl"></i>
          </a>
        </div>
        <!-- Twitter -->
        <div
          v-if="
            establishment.socialMedias && establishment.socialMedias.twitter
          "
        >
          <a
            :href="establishment.socialMedias.twitter"
            target="_blank"
            class="inline-block p-6 rounded-lg bg-blue-400 hover:bg-blue-300 text-white w-20 h-20 flex items-center justify-center"
          >
            <i class="fab fa-twitter text-2xl"></i>
          </a>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
const showFullText = ref(false);
const maxAboutLength = 350;

const toggleText = () => {
  showFullText.value = !showFullText.value;
};

const truncatedAbout = computed(() => {
  if (establishment.value.about.length > maxAboutLength) {
    return showFullText.value
      ? establishment.value.about
      : establishment.value.about.slice(0, maxAboutLength) + '...';
  }
  return establishment.value.about;
});

const bannerImages = computed(() => {
  const url = [
    'https://marketplace.canva.com/EAF_ZFGfAwE/1/0/1600w/canva-banner-para-twitch-montanha-vintage-retr%C3%B4-roxo-nqw7QjAVpKo.jpg',
  ];
  return url;
});

const calcNavPrev = (index: number) => {
  if (index === 0) {
    return `#slide-${bannerImages.value.length}`;
  }
  return `#slide-${index - 1}`;
};

const calcNavNext = (index: number) => {
  if (index === bannerImages.value.length - 1) {
    return `#slide-1`;
  }
  return `#slide-${index + 1}`;
};

const profileImage = computed(() => {
  const url =
    'https://img.freepik.com/vetores-premium/projeto-de-logotipo-de-fantasma-icone-de-halloween-ilustracao-de-fantasia-de-halloween-modelo-de-banner-de-celebracao_557439-5063.jpg';
  return url;
});

const establishment = computed(() => {
  const establishment = {
    name: 'Barbearia do Zé',
    address: 'Rua dos Bobos, 0',
    phone: ['(11) 99999-9999', '(11) 99999-9999'],
    email: '<EMAIL>',
    rating: {
      value: 4.5,
      total: 100,
    },
    socialMedias: {
      facebook: 'https://www.facebook.com',
      instagram: 'https://www.instagram.com',
      twitter: 'https://www.twitter.com',
    },
    about:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Cras elementum ultrices diam. Maecenas ligula massa, varius a, semper congue, euismod non, mi.',
  };
  return establishment;
});

const activeTab = ref<'contact' | 'about' | 'social'>('contact');
</script>
