<template>
  <div>
    <!-- <div class="container-table overflow-auto relative">
      <div class="flex">
        <Qalendar class="" :events="events" >
          <template #header />
        </Qalendar>
        <div>
          <div class="sticky -top-3">
            <DatePicker locale="pt-Br" firstDayOfWeek="sunday" />
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
// import dayjs from "dayjs";
// import { DatePicker, Qalendar } from "qalendar";

// import { useServiceStore } from "~/store/service";
// const serviceStore = useServiceStore();
// const events = ref([]);

// async function getData() {
//   try {
//     const data = await serviceStore.getAll();
//     const customData = data.map((el, i) => {
//       const isPaid = i % 2 == 0;
//       return {
//         id: crypto.randomUUID(),
//         title: el.name,
//         time: {
//           start: dayjs().add(i, "hour").format("YYYY-MM-DD HH:mm"),
//           end: dayjs()
//             .add(el.duration, "minute")
//             .add(i, "hour")
//             .format("YYYY-MM-DD HH:mm"),
//         },
//         isEditable: false,
//         color: isPaid ? "green" : "yellow",
//       };
//     });
//     events.value = customData;
//   } catch (err) {
//     console.error(err);
//   }
// }
// onMounted(getData);
</script>

