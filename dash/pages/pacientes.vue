<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base hide-edit hide-delete :total-items v-model:per_page="per_page" v-model:page="page"
          title="Cadastrar paciente" :columns="columns" :rows="services" class="flex-1" @new="isOpened = true"
          :loading="loading" :actions="[
            { name: 'Visualizar', action: handleViewUser }
          ]">
          <template #filter>
            <div class="flex gap-4 items-center">
              <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
                v-model="filters.name" />
              <input-base class="hidden sm:flex" :options="[
                { value: 'name', label: 'Nome' },
                { value: 'total_spent', label: 'Total Gasto' },
              ]" label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sort_by" />
              <input-base class="hidden sm:flex" :options="[
                { value: 'asc', label: 'Crescente' },
                { value: 'desc', label: 'Decrescente' },
              ]" label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" />
              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button @click="filterModalOpen = true" size="sm" class="mr-3 btn-circle">
                  <AdjustmentsHorizontalIcon class="w-5 " />
                </base-button>
              </div>
            </div>
          </template>
          <template #name="{ row }">
            <td>
              {{ row.user.name }}
            </td>
          </template>
          <template #phone="{ row }">
            <td>
              {{ formatPhone(row.user.phone) }}
            </td>
          </template>
        </table-base>
      </div>
    </div>
    <base-dialog title="Cadastrar novo paciente" v-model="isOpened" @close="resetFormValues">
      <form v-if="isOpened" @submit.prevent="onSubmit" class="sm:pb-14 p-4">
        <div class="flex items-end gap-2 mb-2">
          <input-base type="text" label="Telefone *" v-model="phone" data-maska="(##) #####-####"
            :error="errors.phone" />
          <base-button @click="searchUser" size="sm" type="button" class="!h-[39px] !w-[39px] min-h-0">
            <MagnifyingGlassIcon class="w-5" />
          </base-button>
        </div>

        <div v-if="userSearched" class="space-y-4">

          <input-base type="text" label="Nome *" v-model="name" :readonly="userId" :error="errors.name" />
          <input-base type="email" label="Email" v-model="email" :readonly="userId" :error="errors.email" />
          <input-base v-if="userId && birthday || !userId" type="date" label="Data de Nascimento" v-model="birthday"
            :readonly="userId" :error="errors.birthday" />
        </div>
        <div class="fixed bottom-2 md:bottom-4 left-0 px-4 md:px-10 w-full">
          <base-button v-if="userSearched" type="submit" class="w-full mt-4" :loading="loading">
            {{ userId ? 'Vincular Paciente' : 'Cadastrar Paciente' }}
          </base-button>
        </div>
      </form>
    </base-dialog>

    <base-dialog title="Detalhes do Paciente" v-model="viewUserModal">
      <div v-if="selectedService" class="p-4">
        <div class="flex items-center gap-4 mb-4">
          <nuxt-img class="w-20 h-20 rounded-full" :src="selectedService.user.profile_photo_url" alt="Profile image" />
          <div>
            <h2 class="text-xl font-semibold">{{ selectedService.user.name }}</h2>
            <p class="text-gray-600">{{ selectedService.user.email }}</p>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Telefone</label>
            <p class="mt-1">{{ formatPhone(selectedService.user.phone) }}</p>
          </div>
          <!-- <div>
            <label class="block text-sm font-medium text-gray-700">Total Gasto</label>
            <p class="mt-1">{{ toBrl(selectedService.total_spent) }}</p>
          </div> -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Agendamentos</label>
            <p class="mt-1">{{ selectedService.total_scheduled }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Paciente desde</label>
            <p class="mt-1">{{ new Date(selectedService.user.created_at).toLocaleDateString() }}</p>
          </div>
        </div>

        <!-- Questionário section -->
        <div v-if="selectedService.form_finished" class="mt-6">
          <h3 class="text-lg font-semibold mb-2">Triagem</h3>
          <div v-if="loadingAnswers" class="flex justify-center py-4">
            <div class="loading loading-spinner loading-md"></div>
          </div>
          <div v-else-if="userAnswers.length > 0" class="space-y-2">
            <div v-for="(answer, index) in userAnswers" :key="index" class="">
              <p class="font-medium">{{ answer.question_text }}</p>
              <p class="text-gray-600 mt-1">{{ answer.answer === 'true' ? 'Sim' : answer.answer === 'false' ? 'Não' :
                answer.answer }}</p>
            </div>
          </div>
          <div v-else-if="answersLoaded" class="text-center py-2">
            <p>Nenhuma resposta encontrada.</p>
          </div>
          <!-- <base-button v-if=" && !answersLoaded" @click="fetchUserAnswers" class="w-full mt-2">
            Ver Respostas do Questionário
          </base-button> -->
        </div>
        <div v-else class="mt-4">
          <p class="text-gray-600 italic">Este paciente ainda não preencheu o questionário.</p>
        </div>
      </div>
    </base-dialog>
    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base type="text" label="Buscar" label-style="!-mb-2 ml-2 z-10 text-xs" class="sm:min-w-52"
          v-model="filters.name" />
        <input-base :options="[
          { value: 'name', label: 'Nome' },
          // { value: 'total_spent', label: 'Total Gasto' },
        ]" label="Ordenar por" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.sort_by" />
        <input-base :options="[
          { value: 'asc', label: 'Crescente' },
          { value: 'desc', label: 'Decrescente' },
        ]" label="Ordem" label-style="!-mb-2 ml-2 z-10 text-xs" v-model="filters.direction" />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { AdjustmentsHorizontalIcon, MagnifyingGlassIcon } from "@heroicons/vue/24/solid";
import { ref, onMounted, watch } from "vue";
import { useToast } from "vue-toast-notification";
import { useForm } from "vee-validate";
import * as yup from "yup";
import { api } from "~/server/api";

const schema = yup.object({
  phone: yup.string().required("Telefone é obrigatório"),
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido"),
  birthday: yup.string().nullable()
});

const { handleSubmit, resetForm, defineField, errors } = useForm({
  validationSchema: schema
});
const userSearched = ref(false);
const [phone] = defineField('phone');
const [name] = defineField('name');
const [email] = defineField('email');
const [birthday] = defineField('birthday');

const filterModalOpen = ref(false);
const totalItems = ref(0);
const services = ref([]);
const isOpened = ref(false);
const toast = useToast();
const loading = ref(false);
const per_page = ref(10);
const page = ref(1);
const userFound = ref(false);
const showFields = ref(false);
const userId = ref();
function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length !== 11) {
    return phone;
  }
  const ddd = cleaned.slice(0, 2);
  const prefix = cleaned.slice(2, 7);
  const suffix = cleaned.slice(7);
  return `(${ddd}) ${prefix}-${suffix}`;
}

function toBrl(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(numValue);
}

function resetFormValues() {
  // phone.value = '';
  // name.value = '';
  // email.value = '';
  // birthday.value = '';
  // showFields.value = false;
  userFound.value = false;
  userId.value = null;
  userSearched.value = false;
  resetForm();
}

async function searchUser() {
  if (!phone.value) return;
  try {
    loading.value = true;
    const { data } = await api.get('/search-user', {
      params: { phone: phone.value }
    });
    userSearched.value = true;

    if (data) {
      userFound.value = true;
      name.value = data.name;
      email.value = data.email;
      birthday.value = data.birthday;
      userId.value = data.id;
    } else {
      userFound.value = false;
      showFields.value = true;
      name.value = '';
      email.value = '';
      birthday.value = '';
      userId.value = null;

    }
  } catch (err) {
    console.error(err);
    toast.error("Erro ao buscar usuário");
  } finally {
    loading.value = false;
  }
}

const onSubmit = handleSubmit(async (values) => {
  try {
    loading.value = true;
    if (userId.value) {
      await api.post('/vinculate-client', { ...values, user_id: userId.value });
    } else {
      await api.post("/create-client", values);
    }

    toast.success(userId.value ? "Paciente vinculado com sucesso!" : "Paciente cadastrado com sucesso!");
    isOpened.value = false;
    resetFormValues();
    await getData();

  } catch (err) {
    console.error(err);
    toast.error(userId.value ? "Erro ao vincular paciente!" : "Erro ao cadastrar paciente!");
  } finally {
    loading.value = false;
  }
});

// State for answers
const userAnswers = ref<Array<{ question_text: string, answer: string }>>([]);
const loadingAnswers = ref(false);
const answersLoaded = ref(false);

async function fetchUserAnswers() {
  if (!selectedService.value || !selectedService.value.form_finished) return;

  loadingAnswers.value = true;
  try {
    const { data } = await api.post('/user-answers', {
      company_user_id: selectedService.value.id
    });

    userAnswers.value = data.data;
    answersLoaded.value = true;
  } catch (error) {
    console.error('Error fetching user answers:', error);
    toast.error('Erro ao carregar as respostas do questionário');
  } finally {
    loadingAnswers.value = false;
  }
}

function handleViewUser(row: any) {
  selectedService.value = row;
  viewUserModal.value = true;

  // Reset answers state when opening a new user
  userAnswers.value = [];
  answersLoaded.value = false;
  if (selectedService.value.form_finished) {
    fetchUserAnswers();
  }
}

const selectedService = ref<any>(null);
const viewUserModal = ref(false);

const columns = [
  { label: "Nome", key: "name", sm: true },
  { label: "Telefone", key: "phone" },
  // { label: "Total Gasto", key: "total_spent", type: 'currency' },
  { label: "Agendamentos", key: "total_scheduled" },
];

const filters = ref({
  name: "",
  email: "",
  phone: "",
  sort_by: "name",
  direction: "asc",
});

function handleFilter() {
  filterModalOpen.value = false;
  getData();
}

async function getData() {
  try {
    loading.value = true;
    const { data } = await api("/clients", {
      params: {
        name: filters.value.name,
        email: filters.value.email,
        phone: filters.value.phone,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    services.value = data.data;
    totalItems.value = data.total;
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

const debouncedFetch = () => useDelay(async () => await getData(), 500);

watch(
  filters,
  () => {
    if (filterModalOpen.value) return;
    if (filters.value.name) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

watch(
  [page],
  () => {
    getData();
  }
);
watch(isOpened, () => {
  if (!isOpened.value) {
    resetFormValues();
  }

});
onMounted(async () => {
  await getData();
});
</script>
