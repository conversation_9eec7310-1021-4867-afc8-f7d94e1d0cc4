<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <table-base
          :total-items="totalItems"
          title="Contas"
          :columns="columns"
          :rows="services"
          class="flex-1"
          @new="handleNew"
          @edit="handleEdit"
          @delete="deleteServiceById"
          :loading="loading"
          v-model:per_page="per_page"
          v-model:page="page"
          hide-actions
        >
          <template #filter>
            <div class="flex gap-4 items-center">
              <input-base
                :options="[
                  { value: 'name', label: 'Nome' },
                  { value: 'type', label: 'Tipo' },
                ]"
                class="hidden sm:flex"
                label="Filtrar por"
                v-model="filters.filterBy"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                v-if="filters.filterBy === 'type'"
                :options="[
                  { value: 'profit', label: 'Lucro' },
                  { value: 'loss', label: 'Gasto' },
                ]"
                label="Tipo"
                v-model="filters.value"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                v-if="filters.filterBy === 'name'"
                type="text"
                label="Nome"
                v-model="filters.value"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                :options="[
                  { value: 'total', label: 'Valor' },
                  { value: 'name', label: 'Nome' },
                ]"
                class="hidden sm:flex"
                label="Ordenar por"
                v-model="filters.sortBy"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <input-base
                :options="[
                  { value: 'asc', label: 'Crescente' },
                  { value: 'desc', label: 'Decrescente' },
                ]"
                class="hidden sm:flex"
                label="Ordem"
                v-model="filters.sortDirection"
                label-style="!-mb-2 ml-2 z-10 text-xs"
              />

              <div class="sm:hidden">
                <div class="text-xs -mt-1">Filtros</div>
                <base-button
                  @click="filterModalOpen = true"
                  size="sm"
                  class="mr-3 btn-circle"
                >
                  <AdjustmentsHorizontalIcon class="w-5" />
                </base-button>
              </div>
            </div>
          </template>

          <template #type="{ value }">
            <td class="w-10 md:w-16 text-xs sm:text-base">
              <!-- <PlusCircleIcon
                v-if="value === 'profit'"
                class="w-6 h-6 text-green-600"
              />
              <MinusCircleIcon v-else class="w-6 h-6 text-red-600" /> -->
              {{ value === "profit" ? "Lucro" : "Gasto" }}
            </td>
          </template>

          <template #total="{ value }">
            <td class="text-center w-4 md:w-14 text-sm sm:text-base">
              {{ formatPrice(value) }}
            </td>
          </template>

          <template #created_at="{ value }">
            <td
              class="md:table-cell hidden text-center md:w-20 text-sm sm:text-base"
            >
              {{ dayjs(value).format("DD/MM/YYYY") }}
            </td>
          </template>
        </table-base>
      </div>
    </div>

    <base-dialog title="Adicionar Conta" v-model="isOpened">
      <debits-register-form
        v-if="isOpened"
        @submit="handleFormSubmit"
        :loading="loading"
      />
    </base-dialog>

    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base
          :options="[
            { value: 'name', label: 'Nome' },
            { value: 'type', label: 'Tipo' },
          ]"
          label="Filtrar por"
          v-model="filters.filterBy"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <input-base
          v-if="filters.filterBy === 'type'"
          :options="[
            { value: 'profit', label: 'Entrada' },
            { value: 'loss', label: 'Saída' },
          ]"
          label="Tipo"
          v-model="filters.value"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <input-base
          v-if="filters.filterBy === 'name'"
          type="text"
          label="Nome"
          v-model="filters.value"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <input-base
          :options="[
            { value: 'total', label: 'Valor' },
            { value: 'name', label: 'Nome' },
          ]"
          label="Ordenar por"
          v-model="filters.sortBy"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <input-base
          :options="[
            { value: 'asc', label: 'Crescente' },
            { value: 'desc', label: 'Decrescente' },
          ]"
          label="Ordem"
          v-model="filters.sortDirection"
          label-style="!-mb-2 ml-2 z-10 text-xs"
        />

        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";
import dayjs from "dayjs";
import { MinusCircleIcon, PlusCircleIcon } from "@heroicons/vue/24/outline";
import { AdjustmentsHorizontalIcon } from "@heroicons/vue/24/solid";

const filterModalOpen = ref(false);
const loading = ref(false);
const services = ref([]);
const isOpened = ref(false);
const toast = useToast();
const per_page = ref(10);
const page = ref(1);
const totalItems = ref(0);

const filters = ref({
  filterBy: "name",
  value: "",
  sortBy: "name",
  sortDirection: "asc",
});

const columns = [
  { label: "Nome", key: "name", sm: true },
  { label: "Tipo", key: "type", sm: true },
  { label: "Valor", key: "total", sm: true },
  { label: "Data", key: "created_at" },
];
function formatPrice(price: number) {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "brl",
    maximumFractionDigits: 2,
  }).format(price);
}
async function getData() {
  try {
    loading.value = true;
    const { data } = await api.get("/movements", {
      params: {
        category: "bill",
        filter_by: filters.value.filterBy,
        value: filters.value.value,
        sort_by: filters.value.sortBy,
        direction: filters.value.sortDirection,
        per_page: per_page.value,
        page: page.value,
      },
    });
    services.value = data.data;
    totalItems.value = data.total;
  } catch (err) {
    console.error(err);
    toast.error("Erro ao buscar os dados!");
  } finally {
    loading.value = false;
  }
}

function handleFilter() {
  filterModalOpen.value = false;
  getData();
}

function handleNew() {
  isOpened.value = true;
}

async function handleFormSubmit(values: any) {
  try {
    loading.value = true;
    await api.post("/bill-movements", values);
    toast.success("Conta criada com sucesso!");
    isOpened.value = false;
    await getData();
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  } finally {
    loading.value = false;
  }
}

async function deleteServiceById(item: any) {
  try {
    if (item.id) {
      await api.delete(`/bills/${item.id}`);
      toast.warning("Conta deletada!");
      await getData();
    }
  } catch (err) {
    console.error(err);
    toast.error("Erro na requisição!");
  }
}

function handleEdit(item: any) {
  // TODO: Implement edit functionality
  console.log("Edit item:", item);
}

const debouncedFetch = () => useDelay(async () => await getData(), 500);

watch(
  [filters, page, per_page],
  () => {
    if (filterModalOpen.value) return;
    if (filters.value.value) {
      debouncedFetch();
    } else {
      getData();
    }
  },
  { deep: true }
);

onMounted(getData);
</script>
