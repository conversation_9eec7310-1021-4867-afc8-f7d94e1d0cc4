// This plugin only runs on the client side
export default defineNuxtPlugin(() => {
  // PWA functionality is temporarily disabled
  // No service worker registration for now

  // If you need to re-enable service worker registration, uncomment this code:
  /*
  if (process.client) {
    if ('serviceWorker' in navigator) {
      // Unregister any existing service workers to stop reloading
      navigator.serviceWorker.getRegistrations().then(registrations => {
        for (let registration of registrations) {
          registration.unregister();
        }
      });
    }
  }
  */
});
