import axios from "axios";
import { useLoginStore } from "~/store/user";

export const api = axios.create({
  // baseURL: runtimeConfig.public.API_URL,
  // baseURL: "http://localhost:3333",
});
api.interceptors.request.use((config) => {
  const runtimeConfig = useRuntimeConfig();
  config.baseURL = `${runtimeConfig.public.API_URL}/api`;

  // Log the full URL for debugging
  console.log(`API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);

  const loginStore = useLoginStore();
  const token = loginStore.token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
api.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log(`API Response Success: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
    return response;
  },
  (error) => {
    // Log detailed error information
    console.error('API Error:', {
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      data: error.response?.data,
      message: error.message,
    });

    // Handle authentication errors
    if (error.response?.status === 401 || error.response?.status === 403) {
      const loginStore = useLoginStore();

      // Check if token is expired
      if (loginStore.isTokenExpired) {
        console.log('Token expired, logging out');
      }

      // Logout user on authentication errors
      loginStore.logout();

      // Redirect to login page
      const router = useRouter();
      router.push('/login');
    } else if (error.response?.status === 404) {
      // Log 404 errors with more detail
      console.error('API Endpoint Not Found:', error.config?.url);
    } else if (!error.response) {
      // Network errors or server not reachable
      console.error('Network Error or Server Unreachable:', error.message);
    }

    return Promise.reject(error);
  }
);

export function getServices(url: string) {
  async function create(endpointOrData: unknown, reqData?: object) {
    try {
      let postData;
      // checks if the endpointOrData is a string or a object to be posted as data
      if (typeof endpointOrData === "string") {
        postData = reqData || {};
      } else {
        postData = endpointOrData;
      }

      const response = await api.post(
        typeof endpointOrData === "string" ? endpointOrData : url,
        postData
      );
      return response.data;
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async function deleteById(id: string) {
    try {
      const response = await api.delete(`${url}/${id}`);
      return response.data;
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async function edit(data: object) {
    try {
      // eslint-disable-next-line
      // @ts-ignore
      const response = await api.put(`${url}/${data.id}`, data);
      return response.data;
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async function getAll(reqURL?: string) {
    // const drawerStore = useDrawer();
    try {
      const response = await api.get(reqURL ?? url, {
        // params: {
        //   game: drawerStore.selectedGame,
        // },
      });
      return response.data;
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  return { create, getAll, edit, deleteById, api };
}
