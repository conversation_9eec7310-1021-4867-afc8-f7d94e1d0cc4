// import GithubProvider from "next-auth/providers/github";
// import { Nuxt<PERSON>uth<PERSON>andler } from "#auth";
// export default NuxtAuthHandler({
//   // A secret string you define, to ensure correct encryption
//   secret: "hashit",
//   providers: [
//     // @ts-expect-error You need to use .default here for it to work during SSR. May be fixed via Vite at some point
//     GithubProvider.default({
//       clientId: "Iv1.ba287342888bcf59",
//       clientSecret: "f0c42d2d926832b1d0abce53507dc3febae70645",
//     }),
//   ],
//   pages: {
//     signIn: "/login",
//   },
// });
