import {
  CalendarDaysIcon,
  CameraIcon,
  ChartPieIcon,
  CurrencyDollarIcon,
  UserCircleIcon,
  WrenchScrewdriverIcon,
} from "@heroicons/vue/24/solid";
import { defineStore } from "pinia";
import { computed } from "vue";
export type Routes = {
  to: string;
  label: string;
  icon: typeof CurrencyDollarIcon | string;
};
const routes = computed(() => {
  const routes: Routes[] = [
    {
      to: "/atendimento",
      label: "Atendimento",
      icon: WrenchScrewdriverIcon,
    },
    {
      to: "/agenda",
      label: "Agenda",
      icon: CalendarDaysIcon,
    },
    {
      to: "/historico",
      label: "Hist<PERSON>ric<PERSON>",
      icon: CurrencyDollarIcon,
    },
    {
      to: "/feed",
      label: "Feed",
      icon: CameraIcon,
    },
    {
      to: "/dashboard",
      label: "Faturamento",
      icon: ChartPieIcon,
    },
    {
      to: "/perfil",
      label: "Perfil",
      icon: UserCircleIcon,
    },
  ];

  return routes;
});
export const useNavigationBottom = defineStore("navigationBottom", {
  state: () => ({
    active: false,
    routes,
  }),
  actions: {
    toggleActive() {
      this.active = !this.active;
    },
  },
  persist: true,
});
