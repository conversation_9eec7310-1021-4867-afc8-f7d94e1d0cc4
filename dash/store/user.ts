import { defineStore } from "pinia";
import { api } from "@/server/api";

interface UserInfo {
  name: string;
  email: string;
  image: string;
  phone: string;
}

export const useLoginStore = defineStore("login", {
  state: () => ({
    token: "",
    userInfo: {} as UserInfo,
    tokenExpiry: null as Date | null,
  }),

  getters: {
    isLoggedIn: (state) => !!state.token.length,

    // Check if token is expired
    isTokenExpired: (state) => {
      if (!state.tokenExpiry) return true;
      return new Date() > new Date(state.tokenExpiry);
    },
  },

  actions: {
    login(token: string, expiresAt?: string) {
      this.token = token;

      // Set token expiry if provided
      if (expiresAt) {
        this.tokenExpiry = new Date(expiresAt);
      }
    },

    async logout() {
      try {
        // Call the backend logout endpoint if user is logged in
        if (this.isLoggedIn) {
          await api.post("/auth/logout");
        }
      } catch (error) {
        console.error("Logout error:", error);
      } finally {
        // Clear user data regardless of API call success
        this.token = "";
        this.userInfo = {} as UserInfo;
        this.tokenExpiry = null;
      }
    },

    setUser(user: UserInfo) {
      this.userInfo = user;
    },
  },

  persist: true,
});
