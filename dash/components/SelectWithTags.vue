<template>
  <div class="form-control w-full">
    <label v-if="label" class="text-sm mb-1" :class="labelStyle">
      {{ label }}
    </label>

    <!-- Selected tags display -->
    <div v-if="selectedItems.length > 0" class="flex flex-wrap gap-2 mb-2">
      <div v-for="(item, index) in selectedItems" :key="index" class="badge badge-primary gap-1 p-3">
        {{ getLabel(item) }}
        <button @click="removeItem(item)" class="btn btn-xs btn-circle btn-ghost">
          <XMarkIcon class="w-3 h-3" />
        </button>
      </div>
    </div>

    <!-- Select dropdown -->
    <div ref="componentRef" class="relative w-full">
      <div class="input input-bordered w-full flex items-center cursor-pointer" :class="[
        error ? 'input-error' : '',
        inputClasses
      ]">
        <input type="text" v-model="searchText" @focus="openedDropdown = true" @input="handleInput"
          class="flex-grow border-none focus:outline-none bg-transparent" :placeholder="placeholder" />
        <ChevronUpDownIcon @click="toggleDropdown" class="w-5 h-5 ml-auto cursor-pointer" />
      </div>

      <Teleport to="body">
        <ul
          class="menu fixed z-[9999] flex-nowrap w-full dropdown-content bg-base-100 rounded-box p-2 shadow max-h-60 overflow-y-auto"
          :style="dropdownStyle" v-if="isDropdownOpen">
          <template v-if="loading">
            <li class="flex items-center justify-center py-2">
              <div class="loading loading-spinner loading-sm"></div>
            </li>
          </template>
          <template v-else-if="!availableOptions.length">
            <li class="text-center py-2 text-gray-500">Nenhuma opção disponível</li>
          </template>
          <template v-else>
            <li v-for="(option, i) in availableOptions" :key="i" @mousedown.prevent="selectItem(option)">
              <a>{{ getLabel(option) }}</a>
            </li>
          </template>
        </ul>
      </Teleport>
    </div>

    <div class="text-error text-xs" v-if="error">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { XMarkIcon, ChevronUpDownIcon } from "@heroicons/vue/24/solid";

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
  label: {
    type: String,
    default: "",
  },
  labelStyle: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "Selecione as opções",
  },
  optionLabel: {
    type: String,
    default: undefined,
  },
  optionValue: {
    type: String,
    default: undefined,
  },
  error: {
    type: String,
    default: undefined,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  inputClasses: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue"]);

const selectedItems = ref(props.modelValue || []);
const openedDropdown = ref(false);
const searchText = ref('');
const componentRef = ref<HTMLElement | null>(null);
const dropdownPosition = ref({
  top: 0,
  left: 0,
  width: 0,
});

// Helper function to normalize text for searching (remove accents, lowercase)
function normalizeText(text: string): string {
  return text.normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim();
}

// Computed property to filter out already selected options and filter by search text
const availableOptions = computed(() => {
  const selectedValues = selectedItems.value.map(item =>
    props.optionValue ? item[props.optionValue] : item
  );

  const normalizedSearch = normalizeText(searchText.value);

  return props.options.filter(option => {
    // First filter out already selected options
    const optionValue = props.optionValue ? option[props.optionValue] : option;
    if (selectedValues.includes(optionValue)) return false;

    // Then filter by search text if there is any
    if (normalizedSearch) {
      const optionLabel = getLabel(option);
      const normalizedLabel = normalizeText(optionLabel.toString());
      return normalizedLabel.includes(normalizedSearch);
    }

    // If no search text, include all unselected options
    return true;
  });
});

// Computed property to determine if dropdown should be shown
const isDropdownOpen = computed(() => openedDropdown.value);

// Computed property for dropdown positioning
const dropdownStyle = computed(() => ({
  top: dropdownPosition.value.top + "px",
  left: dropdownPosition.value.left + "px",
  width: dropdownPosition.value.width + "px",
}));

// Helper function to get the display label for an option
function getLabel(option) {
  return props.optionLabel ? option[props.optionLabel] : option;
}

// Function to handle input changes
function handleInput() {
  // Open dropdown when user types
  openedDropdown.value = true;
  updateDropdownPosition();
}

// Function to toggle dropdown visibility
function toggleDropdown() {
  openedDropdown.value = !openedDropdown.value;
  if (openedDropdown.value) {
    updateDropdownPosition();
  }
}

// Function to select an item
function selectItem(option) {
  selectedItems.value = [...selectedItems.value, option];
  emit("update:modelValue", selectedItems.value);
  searchText.value = ''; // Clear search text after selection
  // Keep dropdown open to allow multiple selections
}

// Function to remove an item
function removeItem(item) {
  const itemValue = props.optionValue ? item[props.optionValue] : item;
  selectedItems.value = selectedItems.value.filter(
    selected => (props.optionValue ? selected[props.optionValue] : selected) !== itemValue
  );
  emit("update:modelValue", selectedItems.value);
}

// Function to update dropdown position
function updateDropdownPosition() {
  if (!componentRef.value) return;

  const rect = componentRef.value.getBoundingClientRect();
  dropdownPosition.value = {
    top: rect.bottom + window.scrollY,
    left: rect.left + window.scrollX,
    width: rect.width,
  };
}

// Handle clicks outside the component
function handleClickOutside(event: MouseEvent) {
  // Only close if the click is outside the component
  if (componentRef.value && !componentRef.value.contains(event.target as Node)) {
    openedDropdown.value = false;
  }
}

// Watch for changes in the modelValue prop
watch(() => props.modelValue, (newVal) => {
  selectedItems.value = newVal || [];
}, { deep: true });

// Watch for changes in the dropdown state
watch(isDropdownOpen, (newVal) => {
  if (newVal) {
    updateDropdownPosition();
  } else {
    // Clear search text when dropdown is closed
    searchText.value = '';
  }
});

// Lifecycle hooks
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  window.addEventListener("scroll", updateDropdownPosition);
  window.addEventListener("resize", updateDropdownPosition);
});

onBeforeUnmount(() => {
  document.removeEventListener("click", handleClickOutside);
  window.removeEventListener("scroll", updateDropdownPosition);
  window.removeEventListener("resize", updateDropdownPosition);
});
</script>
