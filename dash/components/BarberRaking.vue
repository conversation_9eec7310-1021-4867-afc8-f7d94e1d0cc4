<template>
  <div class="w-full elevated-card bg-base-200 overflow-auto">
    <table-base
      title="Ranking"
      :columns="columns"
      :rows="RankedData"
      no-create
      hide-actions
    >
      <BaseButton class="mt-auto">Finalizar atendimento</BaseButton>
    </table-base>
  </div>
</template>

<script setup lang="ts">
const columns = [
  { label: "Nome", key: "name" },
  { label: "Email", key: "email" },
  { label: "Serviços", key: "services" },
];

const data = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    services: 50,
  },
  {
    name: "<PERSON>",
    email: "johndo<PERSON>@example.com",
    services: 100,
  },
  {
    name: "<PERSON>",
    email: "robert<PERSON><EMAIL>",
    services: 20,
  },
];

const RankedData = data.sort((a, b) => b.services - a.services);
</script>
