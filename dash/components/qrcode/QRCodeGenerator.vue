<template>
  <div class="flex flex-col items-center">
    <div v-if="loading" class="flex items-center justify-center h-64 w-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    <div v-else-if="qrCodeUrl" class="bg-white p-4 rounded-lg shadow-md">
      <img :src="qrCodeUrl" alt="QR Code" class="h-64 w-64" />
    </div>
    <div v-else class="flex items-center justify-center h-64 w-64 border border-dashed border-gray-300 rounded-lg">
      <p class="text-gray-500">{{ placeholder || 'Clique em "Gerar QR Code" para começar' }}</p>
    </div>

    <div class="mt-6 flex flex-col gap-4 w-full">
      <slot name="actions">
        <button
          @click="generateQRCode"
          class="btn btn-primary w-full"
          :class="{ 'loading': loading }"
          :disabled="loading"
        >
          {{ qrCodeUrl ? 'Gerar Novo QR Code' : 'Gerar QR Code' }}
        </button>

        <button
          v-if="qrCodeUrl"
          @click="downloadQRCode"
          class="btn btn-outline btn-primary w-full"
        >
          Baixar QR Code
        </button>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue';
import { useToast } from 'vue-toast-notification';
import { api } from '~/server/api';
import { useLoginStore } from '~/store/user';

const props = defineProps({
  placeholder: {
    type: String,
    default: ''
  },
  downloadFilename: {
    type: String,
    default: 'qrcode-bayles'
  }
});

const emit = defineEmits(['generated', 'error']);

const qrCodeUrl = ref<string | null>(null);
const loading = ref(false);
const toast = useToast();

// Generate QR code
async function generateQRCode() {
  try {
    loading.value = true;

    // Get the token from the login store
    const loginStore = useLoginStore();
    const token = loginStore.token;

    if (!token) {
      toast.error('Você precisa estar logado para gerar um QR Code.');
      return;
    }

    // Call the backend API to generate a QR code
    const response = await api.post('/qrcode/generate', {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // Set the QR code URL from the response
    qrCodeUrl.value = response.data.qrCodeUrl;

    toast.success('QR Code gerado com sucesso!');
    emit('generated', response.data);
  } catch (error) {
    console.error('Error generating QR code:', error);
    toast.error('Erro ao gerar QR Code. Tente novamente.');
    emit('error', error);
  } finally {
    loading.value = false;
  }
}

// Download QR code as image
function downloadQRCode() {
  if (!qrCodeUrl.value) return;

  const link = document.createElement('a');
  link.href = qrCodeUrl.value;
  link.download = `${props.downloadFilename}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Expose methods to parent component
defineExpose({
  generateQRCode,
  downloadQRCode,
  qrCodeUrl
});
</script>
