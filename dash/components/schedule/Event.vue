<template>
  <div v-if="!isBlocked" class="px-5 py-3 relative z-10">
    <div class="text-lg">
      {{ title }} às
      <b class="text-sm"
        >{{ dayjs(start).format("HH:mm") }} -
        {{ dayjs(end).format("HH:mm") }}</b
      >
    </div>

    <div class="my-1 text-sm">Paciente: {{ client_name }}</div>
    <div class="flex justify-between mt-4">
      <!-- <div class="btn btn-primary btn-ghost">Cancelar</div> -->
      <div
        @click="
          () => {
            finisAppointmentOpen = true;
            closeModal();
          }
        "
        class="btn btn-primary w-full"
      >
        Encerrar agendamento
      </div>
    </div>
    <XMarkIcon
      @click="closeModal"
      class="absolute top-2 right-3 w-6 h-6 text-gray-800 cursor-pointer"
    />
  </div>
  <BaseDialog v-model="finisAppointmentOpen" title="Finalizar agendamento">
    <FinishAppointment
      @finish="handleFinish"
      :loading
      :title
      :id="id"
      :client_id
      :client_name
    />
  </BaseDialog>
</template>
<script setup lang="ts">
// import { PencilSquareIcon } from '@heroicons/vue/24/solid';
import { XMarkIcon } from "@heroicons/vue/24/solid";
import dayjs from "dayjs";
import { api } from "~/server/api";
const client = ref<Client>();
const finisAppointmentOpen = ref(false);
const loading = ref();
interface Client {
  user: User;
}
const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  start: {
    type: String,
    required: true,
  },
  end: {
    type: String,
    required: true,
  },
  service_id: {
    type: Number,
    required: true,
  },
  client_id: {
    type: Number,
    required: true,
  },
  client_name: {
    type: String,
    default: "",
  },
  order_id: {
    type: Number,
    required: false,
  },
  isBlocked: {
    type: Boolean,
    default: false,
  },
});
// onMounted(async () => {
//     const { data } = await api('/client/' + props.client_id)
//     client.value = data
// })
const emit = defineEmits(["finished"]);
function closeModal() {
  document.querySelector(".sx__week-header-content")?.click();
}
async function handleFinish(status: string) {
  loading.value = true;
  await api.post("finish-appointment/" + props.id, {
    status,
  });
  finisAppointmentOpen.value = false;
  emit("finished", props.id);
  loading.value = false;
  closeModal();
}
</script>
