<template>
  <div>
    <!-- Messages Table -->
    <div class="overflow-x-auto">
      <table class="table w-full">
        <thead>
          <tr>
            <th>Data</th>
            <th>Origem</th>
            <th><PERSON><PERSON></th>
            <th>Projeto</th>
            <th>Mensagem</th>
            <th>A<PERSON><PERSON>es</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="6" class="text-center py-4">
              <span class="loading loading-spinner loading-md"></span>
              <span class="ml-2">Carregando mensagens...</span>
            </td>
          </tr>
          <tr v-else-if="messages.length === 0">
            <td colspan="6" class="text-center py-4">
              Nenhuma mensagem encontrada.
            </td>
          </tr>
          <tr v-for="message in messages" :key="message.id" class="hover">
            <td>{{ formatDate(message.date) }}</td>
            <td>
              <div class="font-medium">{{ message.senderName }}</div>
              <div class="text-sm opacity-70">{{ formatPhone(message.senderNumber) }}</div>
            </td>
            <td>
              <div class="font-medium">{{ message.receiverName }}</div>
              <div class="text-sm opacity-70">{{ formatPhone(message.receiverNumber) }}</div>
            </td>
            <td>{{ message.projectName }}</td>
            <td>
              <div class="max-w-xs truncate">{{ message.textMessage }}</div>
            </td>
            <td>
              <button 
                @click="$emit('view', message)" 
                class="btn btn-sm btn-ghost"
              >
                Ver
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- Pagination -->
    <div class="flex justify-between items-center mt-6">
      <div>
        <span>Mostrando {{ messages.length }} de {{ totalMessages }} mensagens</span>
      </div>
      <div class="join">
        <button 
          class="join-item btn"
          :class="{ 'btn-disabled': currentPage === 1 }"
          @click="$emit('page-change', currentPage - 1)"
          :disabled="currentPage === 1 || loading"
        >
          «
        </button>
        <button class="join-item btn">Página {{ currentPage }}</button>
        <button 
          class="join-item btn"
          :class="{ 'btn-disabled': currentPage === totalPages }"
          @click="$emit('page-change', currentPage + 1)"
          :disabled="currentPage === totalPages || loading"
        >
          »
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

interface Message {
  id: number;
  senderName: string;
  senderNumber: string;
  receiverName: string;
  receiverNumber: string;
  textMessage: string;
  projectName: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

const props = defineProps({
  messages: {
    type: Array as () => Message[],
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  currentPage: {
    type: Number,
    default: 1
  },
  totalPages: {
    type: Number,
    default: 1
  },
  totalMessages: {
    type: Number,
    default: 0
  }
});

defineEmits(['view', 'page-change']);

// Format date
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

// Format phone number
function formatPhone(phone: string) {
  if (!phone) return '';
  
  // Format as (XX) XXXXX-XXXX
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
  }
  return phone;
}
</script>
