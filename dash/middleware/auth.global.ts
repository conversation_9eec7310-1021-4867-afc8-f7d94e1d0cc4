import { useLoginStore } from "~/store/user";
import { useToast } from "vue-toast-notification";

export default defineNuxtRouteMiddleware((to) => {
  const nuxtApp = useNuxtApp();
  const loginStore = useLoginStore();
  const toast = useToast();

  // Check if token is valid
  const isLoggedIn = loginStore.isLoggedIn;
  const isTokenExpired = loginStore.isTokenExpired;

  const guestRoutes = [
    "/login",
    "/criar-conta",
    "/forgot-password",
    "/recover-password",
    "/recover-password/",
  ];

  // If token is expired and user is trying to access protected route, logout and redirect to login
  if (isLoggedIn && isTokenExpired && !guestRoutes.includes(to.path)) {
    loginStore.logout();
    toast.error('Sua sessão expirou. Por favor, faça login novamente.');
    // @ts-expect-error: nuxt internal bug
    return nuxtApp.$router.push("/login");
  }

  // If not logged in and trying to access protected route
  if (!guestRoutes.includes(to.path) && !isLoggedIn) {
    // @ts-expect-error: nuxt internal bug
    return nuxtApp.$router.push("/login");
  }

  // If logged in and trying to access guest route
  if (guestRoutes.includes(to.path) && isLoggedIn) {
    // @ts-expect-error: nuxt internal bug
    return nuxtApp.$router.push("/");
  }
});
