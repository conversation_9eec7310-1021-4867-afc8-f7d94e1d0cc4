import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

export default class Message extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'sender_name' })
  declare senderName: string

  @column({ columnName: 'sender_number' })
  declare senderNumber: string

  @column({ columnName: 'receiver_name' })
  declare receiverName: string

  @column({ columnName: 'receiver_number' })
  declare receiverNumber: string

  @column({ columnName: 'text_message' })
  declare textMessage: string

  @column({ columnName: 'project_name' })
  declare projectName: string

  @column({ columnName: 'media_url' })
  declare mediaUrl?: string

  @column({ columnName: 'media_type' })
  declare mediaType?: string

  @column.dateTime()
  declare date: DateTime

  @column({ columnName: 'user_id' })
  declare userId: number | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>
}
