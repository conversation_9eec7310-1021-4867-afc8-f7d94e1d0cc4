import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

export default class WhatsappNumber extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare phoneNumber: string

  @column()
  declare contactName: string

  @column()
  declare projectName: string

  @column()
  declare status: 'pending' | 'active' | 'inactive'

  @column()
  declare qrCodeId: string | null

  @column()
  declare sessionData: string | null

  @column.dateTime()
  declare lastConnected: DateTime | null

  @column({ columnName: 'user_id' })
  declare userId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>
}
