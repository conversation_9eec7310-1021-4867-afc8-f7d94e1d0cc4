import type { HttpContext } from '@adonisjs/core/http'
import hash from '@adonisjs/core/services/hash'
import User from '#models/user'

export default class UsersController {
  /**
   * Display a list of users
   */
  async index({ response }: HttpContext) {
    const users = await User.query().select('id', 'email', 'full_name', 'created_at', 'updated_at')

    return response.json(users)
  }

  /**
   * Display a specific user
   */
  async show({ params, response }: HttpContext) {
    const user = await User.find(params.id)

    if (!user) {
      return response.notFound({ message: 'User not found' })
    }

    return response.json({
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    })
  }

  /**
   * Create a new user
   */
  async store({ request, response }: HttpContext) {
    const data = request.only(['email', 'fullName', 'password'])

    try {
      // Check if user already exists
      const existingUser = await User.findBy('email', data.email)
      if (existingUser) {
        return response.conflict({ message: 'Email already in use' })
      }

      const hashedPassword = await hash.make(data.password)

      const user = new User()
      user.email = data.email
      user.fullName = data.fullName
      user.password = hashedPassword
      await user.save()

      return response.created({
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })
    } catch (error) {
      return response.internalServerError({ message: 'Something went wrong' })
    }
  }

  /**
   * Update a user
   */
  async update({ params, request, response }: HttpContext) {
    const data = request.only(['fullName', 'password'])

    try {
      const user = await User.find(params.id)

      if (!user) {
        return response.notFound({ message: 'User not found' })
      }

      if (data.fullName !== undefined) {
        user.fullName = data.fullName
      }

      if (data.password) {
        user.password = await hash.make(data.password)
      }

      await user.save()

      return response.json({
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })
    } catch (error) {
      return response.internalServerError({ message: 'Something went wrong' })
    }
  }

  /**
   * Delete a user
   */
  async destroy({ params, response }: HttpContext) {
    try {
      const user = await User.find(params.id)

      if (!user) {
        return response.notFound({ message: 'User not found' })
      }

      await user.delete()

      return response.noContent()
    } catch (error) {
      return response.internalServerError({ message: 'Something went wrong' })
    }
  }
}
