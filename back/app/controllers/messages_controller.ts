import type { HttpContext } from '@adonisjs/core/http'
import Message from '#models/message'
import { DateTime } from 'luxon'

export default class MessagesController {
  /**
   * Display a list of messages with optional filtering
   */
  async index({ request, response }: HttpContext) {
    // Get query parameters for filtering
    const {
      senderNumber,
      receiverNumber,
      projectName,
      startDate,
      endDate,
      page = 1,
      limit = 20,
    } = request.qs()

    // Build the query
    const query = Message.query()

    if (senderNumber) {
      query.where('sender_number', senderNumber)
    }

    if (receiverNumber) {
      query.where('receiver_number', receiverNumber)
    }

    if (projectName) {
      query.where('project_name', projectName)
    }

    // Date range filter
    if (startDate) {
      query.where('date', '>=', DateTime.fromISO(startDate).toSQL())
    }

    if (endDate) {
      query.where('date', '<=', DateTime.fromISO(endDate).toSQL())
    }

    // Get paginated results
    const messages = await query
      .orderBy('date', 'desc')
      .paginate(Number(page), Number(limit))

    return response.json(messages)
  }

  /**
   * Display a specific message
   */
  async show({ params, response }: HttpContext) {
    const message = await Message.find(params.id)

    if (!message) {
      return response.notFound({ message: 'Message not found' })
    }

    return response.json(message)
  }

  /**
   * Create a new message
   */
  async store({ request, response, auth }: HttpContext) {
    // Get the authenticated user if available
    const user = auth.user

    const data = request.only([
      'senderName',
      'senderNumber',
      'receiverName',
      'receiverNumber',
      'textMessage',
      'projectName',
      'date',
    ])

    try {
      const message = new Message()
      message.senderName = data.senderName
      message.senderNumber = data.senderNumber
      message.receiverName = data.receiverName
      message.receiverNumber = data.receiverNumber
      message.textMessage = data.textMessage
      message.projectName = data.projectName
      message.date = DateTime.fromISO(data.date)

      if (user) {
        message.userId = user.id
      }

      await message.save()

      return response.created(message)
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to create message',
        error: error.message
      })
    }
  }

  /**
   * Update a message
   */
  async update({ params, request, response }: HttpContext) {
    const message = await Message.find(params.id)

    if (!message) {
      return response.notFound({ message: 'Message not found' })
    }

    const data = request.only([
      'senderName',
      'senderNumber',
      'receiverName',
      'receiverNumber',
      'textMessage',
      'projectName',
      'date',
    ])

    try {
      message.senderName = data.senderName || message.senderName
      message.senderNumber = data.senderNumber || message.senderNumber
      message.receiverName = data.receiverName || message.receiverName
      message.receiverNumber = data.receiverNumber || message.receiverNumber
      message.textMessage = data.textMessage || message.textMessage
      message.projectName = data.projectName || message.projectName

      if (data.date) {
        message.date = DateTime.fromISO(data.date)
      }

      await message.save()

      return response.json(message)
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to update message',
        error: error.message
      })
    }
  }

  /**
   * Delete a message
   */
  async destroy({ params, response }: HttpContext) {
    const message = await Message.find(params.id)

    if (!message) {
      return response.notFound({ message: 'Message not found' })
    }

    try {
      await message.delete()

      return response.noContent()
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to delete message',
        error: error.message
      })
    }
  }
}
