import type { HttpContext } from '@adonisjs/core/http'
import hash from '@adonisjs/core/services/hash'
import User from '#models/user'

export default class SimpleAuthController {
  /**
   * Simple login that doesn't rely on the AdonisJS auth system
   */
  async login({ request, response }: HttpContext) {
    const { email, password } = request.only(['email', 'password'])

    try {
      // Find user by email
      const user = await User.findBy('email', email)
      
      if (!user) {
        return response.unauthorized({ error: 'Invalid credentials' })
      }

      // Verify password
      const isPasswordValid = await hash.verify(user.password, password)
      
      if (!isPasswordValid) {
        return response.unauthorized({ error: 'Invalid credentials' })
      }

      // Generate a simple token
      const token = `token_${Date.now()}_${Math.random().toString(36).substring(2)}`
      
      // Calculate expiry date (7 days from now)
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7)

      return response.json({
        token,
        type: 'bearer',
        expires_at: expiresAt.toISOString(),
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
        }
      })
    } catch (error) {
      console.error('Login error:', error)
      return response.internalServerError({
        error: 'An error occurred during login',
        message: error.message
      })
    }
  }

  /**
   * Get the authenticated user
   */
  async me({ request, response }: HttpContext) {
    try {
      // In a real implementation, we would verify the token
      // For now, we'll just return a mock user
      return response.json({
        id: 1,
        email: '<EMAIL>',
        fullName: 'Test User',
      })
    } catch (error) {
      console.error('Get user error:', error)
      return response.internalServerError({
        error: 'An error occurred while fetching user data',
        message: error.message
      })
    }
  }
}
