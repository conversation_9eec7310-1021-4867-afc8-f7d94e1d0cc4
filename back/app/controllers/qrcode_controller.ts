import type { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import Message from '#models/message'
import WhatsappNumber from '#models/whatsapp_number'
import { randomUUID } from 'node:crypto'

export default class QrcodeController {
  /**
   * List all registered WhatsApp numbers
   */
  async index({ request, response }: HttpContext) {
    try {
      // Extract token from Authorization header
      const authHeader = request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.unauthorized({ error: 'Invalid token' })
      }

      // In a real implementation, we would verify the token and get the user
      // For now, we'll use a mock user ID
      const userId = 1

      // Get all WhatsApp numbers for the user
      const whatsappNumbers = await WhatsappNumber.query()
        .where('user_id', userId)
        .orderBy('created_at', 'desc')

      return response.ok(whatsappNumbers)
    } catch (error) {
      console.error('Error fetching WhatsApp numbers:', error)
      return response.internalServerError({
        error: 'Failed to fetch WhatsApp numbers',
        message: error.message,
      })
    }
  }

  /**
   * Create a new WhatsApp number (pending status)
   */
  async store({ request, response }: HttpContext) {
    try {
      // Extract token from Authorization header
      const authHeader = request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.unauthorized({ error: 'Invalid token' })
      }

      // In a real implementation, we would verify the token and get the user
      // For now, we'll use a mock user ID
      const userId = 1

      // Get request data
      const { phoneNumber, contactName, projectName } = request.only([
        'phoneNumber',
        'contactName',
        'projectName',
      ])

      // Validate required fields
      if (!phoneNumber || !contactName || !projectName) {
        return response.badRequest({
          error: 'Missing required fields',
          message: 'Phone number, contact name, and project name are required',
        })
      }

      // Create a new WhatsApp number
      const whatsappNumber = new WhatsappNumber()
      whatsappNumber.phoneNumber = phoneNumber
      whatsappNumber.contactName = contactName
      whatsappNumber.projectName = projectName
      whatsappNumber.status = 'pending'
      whatsappNumber.userId = userId

      await whatsappNumber.save()

      return response.created(whatsappNumber)
    } catch (error) {
      console.error('Error creating WhatsApp number:', error)
      return response.internalServerError({
        error: 'Failed to create WhatsApp number',
        message: error.message,
      })
    }
  }

  /**
   * Generate a QR code for a WhatsApp number
   */
  async generate({ request, response, params }: HttpContext) {
    try {
      // Extract token from Authorization header
      const authHeader = request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.unauthorized({ error: 'Invalid token' })
      }

      // In a real implementation, we would verify the token and get the user
      // For now, we'll use a mock user ID
      const userId = 1

      // Find the WhatsApp number
      const whatsappNumber = await WhatsappNumber.query()
        .where('id', params.id)
        .where('user_id', userId)
        .first()

      if (!whatsappNumber) {
        return response.notFound({ error: 'WhatsApp number not found' })
      }

      // Generate a unique ID for the QR code
      const qrCodeId = randomUUID()

      // Update the WhatsApp number with the QR code ID
      whatsappNumber.qrCodeId = qrCodeId
      await whatsappNumber.save()

      // Import Baileys service
      const baileysServiceModule = await import('#services/baileys_service')
      const baileysService = baileysServiceModule.default

      // Initialize WhatsApp connection and get QR code
      const qrCode = await baileysService.initializeConnection(qrCodeId, whatsappNumber.id)

      if (!qrCode) {
        return response.internalServerError({
          error: 'Failed to generate QR code',
          message: 'QR code generation timed out',
        })
      }

      // Generate a QR code URL using a public service
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrCode)}`

      return response.ok({
        id: whatsappNumber.id,
        qrCodeId,
        qrCodeUrl,
        phoneNumber: whatsappNumber.phoneNumber,
        contactName: whatsappNumber.contactName,
        projectName: whatsappNumber.projectName,
        status: whatsappNumber.status,
        createdAt: whatsappNumber.createdAt.toISO(),
      })
    } catch (error) {
      console.error('Error generating QR code:', error)
      return response.internalServerError({
        error: 'Failed to generate QR code',
        message: error.message,
      })
    }
  }

  /**
   * Activate a WhatsApp number (simulate QR code scan)
   */
  async activate({ request, response, params }: HttpContext) {
    try {
      // Extract token from Authorization header
      const authHeader = request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.unauthorized({ error: 'Invalid token' })
      }

      // In a real implementation, we would verify the token and get the user
      // For now, we'll use a mock user ID
      const userId = 1

      // Find the WhatsApp number
      const whatsappNumber = await WhatsappNumber.query()
        .where('id', params.id)
        .where('user_id', userId)
        .first()

      if (!whatsappNumber) {
        return response.notFound({ error: 'WhatsApp number not found' })
      }

      // Update the WhatsApp number status to active
      whatsappNumber.status = 'active'
      await whatsappNumber.save()

      // Create a message to record the activation
      const message = new Message()
      message.senderName = whatsappNumber.contactName
      message.senderNumber = whatsappNumber.phoneNumber
      message.receiverName = 'System'
      message.receiverNumber = '0000000000' // System number
      message.textMessage = `WhatsApp number activated: ${whatsappNumber.phoneNumber}`
      message.projectName = whatsappNumber.projectName
      message.date = DateTime.now()
      message.userId = userId

      await message.save()

      return response.ok({
        success: true,
        message: 'WhatsApp number activated successfully',
        data: whatsappNumber,
      })
    } catch (error) {
      console.error('Error activating WhatsApp number:', error)
      return response.internalServerError({
        error: 'Failed to activate WhatsApp number',
        message: error.message,
      })
    }
  }

  /**
   * Deactivate a WhatsApp number
   */
  async deactivate({ request, response, params }: HttpContext) {
    try {
      // Extract token from Authorization header
      const authHeader = request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.unauthorized({ error: 'Invalid token' })
      }

      // In a real implementation, we would verify the token and get the user
      // For now, we'll use a mock user ID
      const userId = 1

      // Find the WhatsApp number
      const whatsappNumber = await WhatsappNumber.query()
        .where('id', params.id)
        .where('user_id', userId)
        .first()

      if (!whatsappNumber) {
        return response.notFound({ error: 'WhatsApp number not found' })
      }

      // Update the WhatsApp number status to inactive
      whatsappNumber.status = 'inactive'
      await whatsappNumber.save()

      return response.ok({
        success: true,
        message: 'WhatsApp number deactivated successfully',
        data: whatsappNumber,
      })
    } catch (error) {
      console.error('Error deactivating WhatsApp number:', error)
      return response.internalServerError({
        error: 'Failed to deactivate WhatsApp number',
        message: error.message,
      })
    }
  }

  /**
   * Delete a WhatsApp number
   */
  async destroy({ request, response, params }: HttpContext) {
    try {
      // Extract token from Authorization header
      const authHeader = request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.unauthorized({ error: 'Invalid token' })
      }

      // In a real implementation, we would verify the token and get the user
      // For now, we'll use a mock user ID
      const userId = 1

      // Find the WhatsApp number
      const whatsappNumber = await WhatsappNumber.query()
        .where('id', params.id)
        .where('user_id', userId)
        .first()

      if (!whatsappNumber) {
        return response.notFound({ error: 'WhatsApp number not found' })
      }

      // Delete the WhatsApp number
      await whatsappNumber.delete()

      return response.noContent()
    } catch (error) {
      console.error('Error deleting WhatsApp number:', error)
      return response.internalServerError({
        error: 'Failed to delete WhatsApp number',
        message: error.message,
      })
    }
  }
}
