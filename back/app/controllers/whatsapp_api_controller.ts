import type { HttpContext } from '@adonisjs/core/http'
import jwt from 'jsonwebtoken'
import env from '#start/env'
import WhatsappNumber from '#models/whatsapp_number'
import Message from '#models/message'
import { DateTime } from 'luxon'
import { EncryptJWT, jwtDecrypt } from 'jose'
import { randomBytes } from 'crypto'

interface JWTPayload {
  message: string
  client_number: string
  project_name: string
  // secret: string
}

export default class WhatsappApiController {
  /**
   * Send WhatsApp message via JWT token
   */

  async sendMessage({ request, response }: HttpContext) {
    try {
      // const { token } = request.only(['token'])

      const token = await this.generateJWEToken()
      // return response.json({ token })
      if (!token) {
        return response.badRequest({
          error: 'Missing token',
          message: 'JWT token is required',
        })
      }

      // Use environment variable for secret
      const secret = env.get('WHATSAPP_JWT_SECRET') || 'a-string-secret-at-least-256-bits-long'
      const secretKey = new TextEncoder().encode(secret)

      try {
        // Decrypt and verify the JWE
        const { payload: decodedPayload } = await jwtDecrypt(token, secretKey, {
          contentEncryptionAlgorithms: ['A256GCM'],
          keyManagementAlgorithms: ['dir'],
        })

        // Validate required fields
        const { message, client_number, project_name } = decodedPayload

        if (!message || !client_number || !project_name) {
          return response.badRequest({
            error: 'Missing required fields',
            message: 'JWT payload must contain: message, client_number, and project_name',
          })
        }

        // Find the WhatsApp number by project name
        const whatsappNumber = await WhatsappNumber.query()
          .where('project_name', project_name)
          .where('status', 'active')
          .first()

        if (!whatsappNumber) {
          return response.notFound({
            error: 'WhatsApp number not found',
            message: `No active WhatsApp number found for project: ${project_name}`,
          })
        }

        // Rest of your WhatsApp logic remains the same...
        const baileysService = (await import('#services/baileys_service')).default
        const connections = baileysService.getConnections()
        const connection = connections.find((conn) => conn.whatsappNumberId === whatsappNumber.id)

        if (!connection || connection.state !== 'connected') {
          return response.serviceUnavailable({
            connections,
            error: 'WhatsApp not connected ' + connection,
            message: `WhatsApp number for project ${project_name} is not currently connected`,
          })
        }

        let formattedClientNumber = client_number.replace(/\D/g, '')
        if (!formattedClientNumber.startsWith('55')) {
          formattedClientNumber = '55' + formattedClientNumber
        }
        formattedClientNumber += '@s.whatsapp.net'

        await connection.socket.sendMessage(formattedClientNumber, { text: message })

        const messageRecord = new Message()
        messageRecord.senderName = whatsappNumber.contactName
        messageRecord.senderNumber = whatsappNumber.phoneNumber
        messageRecord.receiverName = 'Client'
        messageRecord.receiverNumber = client_number
        messageRecord.textMessage = message
        messageRecord.projectName = project_name
        messageRecord.date = DateTime.now()
        messageRecord.userId = whatsappNumber.userId

        await messageRecord.save()

        return response.ok({
          success: true,
          message: 'Message sent successfully',
          data: {
            messageId: messageRecord.id,
            from: whatsappNumber.phoneNumber,
            to: client_number,
            message: message,
            project: project_name,
            sentAt: messageRecord.date.toISO(),
          },
        })
      } catch (error) {
        console.error('JWE Verification Error:', error)
        return response.unauthorized({
          error: 'Invalid token',
          message: 'Could not decrypt and verify token: ' + error.message,
        })
      }
    } catch (error) {
      console.error('Error in sendMessage:', error)
      return response.internalServerError({
        error: 'Internal server error' + error.message,
        message: 'An unexpected error occurred',
      })
    }
  }

  /**
   * Get status of WhatsApp connections by project
   */
  async getStatus({ request, response }: HttpContext) {
    try {
      // Get the JWT token from the request body or query
      const token = request.input('token') || request.qs().token

      if (!token) {
        return response.badRequest({
          error: 'Missing token',
          message: 'JWT token is required',
        })
      }

      // Decode and verify the token
      let decodedPayload: JWTPayload
      try {
        decodedPayload = jwt.decode(token) as JWTPayload

        if (!decodedPayload || typeof decodedPayload !== 'object') {
          throw new Error('Invalid payload')
        }

        // Verify the secret
        const expectedSecret = env.get('WHATSAPP_JWT_SECRET')
        if (decodedPayload.secret !== expectedSecret) {
          return response.unauthorized({
            error: 'Invalid secret',
            message: 'The provided secret does not match the expected value',
          })
        }

        // Verify the JWT signature
        jwt.verify(token, expectedSecret)
      } catch (error) {
        return response.unauthorized({
          error: 'Token verification failed',
          message: 'JWT token is invalid',
        })
      }

      // Get all WhatsApp numbers and their connection status
      const whatsappNumbers = await WhatsappNumber.all()
      const baileysService = (await import('#services/baileys_service')).default
      const connections = baileysService.getConnections()

      const status = whatsappNumbers.map((number) => {
        const connection = connections.find((conn) => conn.whatsappNumberId === number.id)

        return {
          id: number.id,
          projectName: number.projectName,
          phoneNumber: number.phoneNumber,
          contactName: number.contactName,
          status: number.status,
          connected: connection ? connection.state === 'connected' : false,
          lastConnected: number.lastConnected?.toISO() || null,
        }
      })

      return response.ok({
        success: true,
        data: status,
      })
    } catch (error) {
      console.error('Error in getStatus:', error)
      return response.internalServerError({
        error: 'Internal server error',
        message: 'An unexpected error occurred',
      })
    }
  }

  async generateJWEToken() {
    // Your secret - in production use process.env.JWT_SECRET
    const secret = 'B4DC9A8F6F45E5E66BD68D62CC8B6A12'
    const secretKey = new TextEncoder().encode(secret)

    // Your payload data
    const payload = {
      message: 'Teste',
      client_number: '558391364928',
      project_name: 'Novo Projeto',
      iat: Math.floor(Date.now() / 1000), // issued at
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 2, // expires in 2 hours
    }

    // Generate the encrypted JWT
    const jwe = await new EncryptJWT(payload)
      .setProtectedHeader({
        alg: 'dir', // Direct encryption with shared key
        enc: 'A256GCM', // AES-GCM with 256-bit key
      })
      .setIssuedAt()
      .setExpirationTime('2h')
      .encrypt(secretKey)

    return jwe
  }
}
