/**
 * Baileys Service for Bayles
 *
 * This service provides a simplified interface for working with the Baileys WhatsApp library.
 */

import {
  makeWASocket,
  useMultiFileAuthState,
  DisconnectReason,
  WASocket
} from '@whiskeysockets/baileys'
import { Boom } from '@hapi/boom'
import { EventEmitter } from 'node:events'
import fs from 'node:fs'
import path from 'node:path'
import app from '@adonisjs/core/services/app'
import WhatsappNumber from '#models/whatsapp_number'
import { DateTime } from 'luxon'
import pino from 'pino'

// Create a logger
const logger = pino({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
    },
  },
})

// Types
export interface BaileysConnection {
  socket: WASocket
  sessionId: string
  whatsappNumberId: number
  qrCode: string | null
  state: 'connecting' | 'connected' | 'disconnected' | 'qr_ready'
  lastDisconnectReason?: string
}

/**
 * Main Baileys service class
 */
class BaileysService {
  private static instance: BaileysService
  private connections: Map<string, BaileysConnection> = new Map()
  private eventEmitter = new EventEmitter()
  private sessionsDir: string

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Set up sessions directory
    this.sessionsDir = path.join(app.makePath('tmp'), 'baileys_sessions')
    this.ensureSessionsDirectory()

    logger.info('BaileysService initialized')
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): BaileysService {
    if (!BaileysService.instance) {
      BaileysService.instance = new BaileysService()
    }
    return BaileysService.instance
  }

  /**
   * Ensure the sessions directory exists
   */
  private ensureSessionsDirectory(): void {
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true })
    }
  }

  /**
   * Get the path for a specific session
   * @param sessionId Session ID
   */
  private getSessionPath(sessionId: string): string {
    const sessionPath = path.join(this.sessionsDir, sessionId)

    // Create session directory if it doesn't exist
    if (!fs.existsSync(sessionPath)) {
      fs.mkdirSync(sessionPath, { recursive: true })
    }

    return sessionPath
  }

  /**
   * Initialize a new WhatsApp connection
   * @param sessionId Unique session ID
   * @param whatsappNumberId WhatsApp number ID in the database
   */
  public async initializeConnection(sessionId: string, whatsappNumberId: number): Promise<string> {
    try {
      logger.info(`Initializing connection for session ${sessionId}`)

      // Get the session directory
      const sessionDir = this.getSessionPath(sessionId)

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir)

      // Create a custom saveCreds function that also saves to the database
      const customSaveCreds = async () => {
        // Save credentials to file system
        await saveCreds()

        // Save session data to the database
        const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
        if (whatsappNumber) {
          whatsappNumber.sessionData = JSON.stringify({
            sessionId,
            lastConnected: new Date().toISOString(),
          })
          whatsappNumber.lastConnected = DateTime.now()
          await whatsappNumber.save()

          logger.info(`Saved credentials for WhatsApp number ${whatsappNumberId}`)
        }
      }

      // Create socket connection
      const socket = makeWASocket({
        auth: state,
        logger,
        browser: ['Bayles WhatsApp', 'Chrome', '1.0.0'],
      })

      // Create connection object
      const connection: BaileysConnection = {
        socket,
        sessionId,
        whatsappNumberId,
        qrCode: null,
        state: 'connecting',
      }

      // Store the connection
      this.connections.set(sessionId, connection)

      // Set up connection event handlers
      this.setupConnectionHandlers(connection, customSaveCreds)

      // Return a promise that resolves when QR code is ready
      return new Promise((resolve) => {
        // Listen for connection updates
        socket.ev.on('connection.update', (update) => {
          const { connection: connectionState, lastDisconnect, qr } = update

          // Handle QR code generation
          if (qr) {
            logger.info(`QR code generated for session ${sessionId}`)

            // Update connection object
            const conn = this.connections.get(sessionId)
            if (conn) {
              conn.qrCode = qr
              conn.state = 'qr_ready'
            }

            // Resolve with the QR code
            resolve(qr)
          }

          // Handle connection state changes
          if (connectionState === 'close') {
            const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode

            // If the connection was closed due to a restart required, we don't need to do anything
            // The reconnection will be handled by the setupConnectionHandlers method
            if (statusCode === DisconnectReason.restartRequired) {
              logger.info(`Connection restart required for session ${sessionId}`)
            }
          }
        })

        // Set a timeout in case QR code is not generated
        setTimeout(() => {
          if (!this.connections.get(sessionId)?.qrCode) {
            logger.warn(`QR code generation timed out for session ${sessionId}`)
            resolve('')
          }
        }, 30000) // 30 seconds timeout
      })
    } catch (error) {
      logger.error(`Error initializing connection for session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Set up connection event handlers
   * @param connection Connection object
   * @param saveCreds Function to save credentials
   */
  private setupConnectionHandlers(
    connection: BaileysConnection,
    saveCreds: () => Promise<void>
  ): void {
    const { socket, sessionId, whatsappNumberId } = connection

    // Handle connection updates
    socket.ev.on('connection.update', async (update) => {
      const { connection: connectionState, lastDisconnect } = update

      // Update connection object
      const conn = this.connections.get(sessionId)
      if (!conn) return;

      if (connectionState) {
        conn.state = connectionState === 'open' ? 'connected' : 'disconnected'

        if (connectionState === 'open') {
          logger.info(`Connection established for session ${sessionId}`)

          // Get the connected WhatsApp number information
          const phoneNumber = socket.user?.id?.split(':')[0] || ''
          const contactName = socket.user?.name || ''

          logger.info(`Connected WhatsApp: ${phoneNumber} (${contactName})`)

          // Update WhatsApp number in database with actual phone number and name
          await this.updateWhatsappNumber(whatsappNumberId, {
            phoneNumber,
            contactName,
            status: 'active'
          })

          // Save credentials
          await saveCreds()
        } else if (connectionState === 'close') {
          const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode
          const shouldReconnect = statusCode !== DisconnectReason.loggedOut

          conn.lastDisconnectReason = DisconnectReason[statusCode] || 'unknown'

          logger.info(`Connection closed for session ${sessionId}. Status code: ${statusCode}`)

          if (shouldReconnect) {
            logger.info(`Attempting to reconnect session ${sessionId}...`)

            // If the connection was closed due to a restart required, we need to reconnect
            if (statusCode === DisconnectReason.restartRequired) {
              logger.info(`Connection restart required for session ${sessionId}`)
            }

            // Remove the old connection
            this.connections.delete(sessionId)

            // Wait a bit before reconnecting to avoid rapid reconnection attempts
            setTimeout(async () => {
              try {
                // Initialize a new connection
                await this.initializeConnection(sessionId, whatsappNumberId)
              } catch (error) {
                logger.error(`Error reconnecting session ${sessionId}:`, error)
              }
            }, 3000); // Wait 3 seconds before reconnecting
          } else {
            // Update WhatsApp number status in database
            await this.updateWhatsappNumberStatus(whatsappNumberId, 'inactive')
          }
        }
      }
    })

    // Handle credentials update
    socket.ev.on('creds.update', async () => {
      logger.info(`Credentials updated for session ${sessionId}`)
      await saveCreds()
    })
  }

  /**
   * Update WhatsApp number status in database
   * @param whatsappNumberId WhatsApp number ID
   * @param status New status
   */
  private async updateWhatsappNumberStatus(
    whatsappNumberId: number,
    status: 'active' | 'inactive' | 'pending'
  ): Promise<void> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
      if (whatsappNumber) {
        whatsappNumber.status = status

        // Update lastConnected timestamp if status is active
        if (status === 'active') {
          whatsappNumber.lastConnected = DateTime.now()
        }

        await whatsappNumber.save()
      }
    } catch (error) {
      logger.error(`Error updating WhatsApp number status:`, error)
    }
  }

  /**
   * Update WhatsApp number in database with actual information
   * @param whatsappNumberId WhatsApp number ID
   * @param data Update data
   */
  private async updateWhatsappNumber(
    whatsappNumberId: number,
    data: {
      phoneNumber?: string
      contactName?: string
      status?: 'active' | 'inactive' | 'pending'
    }
  ): Promise<void> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
      if (whatsappNumber) {
        // Only update phone number if it's provided and the current one is a placeholder
        if (data.phoneNumber && (whatsappNumber.phoneNumber === '0000000000' || whatsappNumber.phoneNumber.startsWith('temp-'))) {
          whatsappNumber.phoneNumber = data.phoneNumber
        }

        // Update contact name if provided
        if (data.contactName) {
          whatsappNumber.contactName = data.contactName
        }

        // Update status if provided
        if (data.status) {
          whatsappNumber.status = data.status
        }

        // Always update lastConnected timestamp
        whatsappNumber.lastConnected = DateTime.now()

        await whatsappNumber.save()
        logger.info(`Updated WhatsApp number ${whatsappNumberId} with actual information`)
      }
    } catch (error) {
      logger.error(`Error updating WhatsApp number:`, error)
    }
  }

  /**
   * Get all active connections
   */
  public getConnections(): BaileysConnection[] {
    return Array.from(this.connections.values())
  }

  /**
   * Get a specific connection
   * @param sessionId Session ID
   */
  public getConnection(sessionId: string): BaileysConnection | undefined {
    return this.connections.get(sessionId)
  }
}

// Export singleton instance
export default BaileysService.getInstance()
