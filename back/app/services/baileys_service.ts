/**
 * Baileys Service for Bayles
 *
 * This service provides a simplified interface for working with the Baileys WhatsApp library.
 */

import {
  makeWASocket,
  useMultiFileAuthState,
  DisconnectReason,
  WASocket
} from '@whiskeysockets/baileys'
import { Boom } from '@hapi/boom'
import { EventEmitter } from 'node:events'
import fs from 'node:fs'
import path from 'node:path'
import app from '@adonisjs/core/services/app'
import WhatsappNumber from '#models/whatsapp_number'
import { DateTime } from 'luxon'
import pino from 'pino'

// Create a logger
const logger = pino({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
    },
  },
})

// Types
export interface BaileysConnection {
  socket: WASocket
  sessionId: string
  whatsappNumberId: number
  qrCode: string | null
  state: 'connecting' | 'connected' | 'disconnected' | 'qr_ready'
  lastDisconnectReason?: string
}

/**
 * Main Baileys service class
 */
class BaileysService {
  private static instance: BaileysService
  private connections: Map<string, BaileysConnection> = new Map()
  private eventEmitter = new EventEmitter()
  private sessionsDir: string

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Set up sessions directory
    this.sessionsDir = path.join(app.makePath('tmp'), 'baileys_sessions')
    this.ensureSessionsDirectory()

    logger.info('BaileysService initialized')
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): BaileysService {
    if (!BaileysService.instance) {
      BaileysService.instance = new BaileysService()
    }
    return BaileysService.instance
  }

  /**
   * Ensure the sessions directory exists
   */
  private ensureSessionsDirectory(): void {
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true })
    }
  }

  /**
   * Get the path for a specific session
   * @param sessionId Session ID
   */
  private getSessionPath(sessionId: string): string {
    const sessionPath = path.join(this.sessionsDir, sessionId)

    // Create session directory if it doesn't exist
    if (!fs.existsSync(sessionPath)) {
      fs.mkdirSync(sessionPath, { recursive: true })
    }

    return sessionPath
  }

  /**
   * Initialize a new WhatsApp connection
   * @param sessionId Unique session ID
   * @param whatsappNumberId WhatsApp number ID in the database
   * @param isReconnection Whether this is a reconnection attempt
   */
  public async initializeConnection(sessionId: string, whatsappNumberId: number, isReconnection: boolean = false): Promise<string> {
    try {
      logger.info(`Initializing connection for session ${sessionId} (reconnection: ${isReconnection})`)

      // Get the session directory
      const sessionDir = this.getSessionPath(sessionId)

      // Check if we have valid session credentials
      const hasValidCreds = await this.hasValidSession(sessionId)
      logger.info(`Session ${sessionId} has valid credentials: ${hasValidCreds}`)

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir)

      // Create a custom saveCreds function that also saves to the database
      const customSaveCreds = async () => {
        // Save credentials to file system
        await saveCreds()

        // Save session data to the database
        const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
        if (whatsappNumber) {
          whatsappNumber.sessionData = JSON.stringify({
            sessionId,
            lastConnected: new Date().toISOString(),
            hasValidCredentials: true,
          })
          whatsappNumber.lastConnected = DateTime.now()
          await whatsappNumber.save()

          logger.info(`Saved credentials for WhatsApp number ${whatsappNumberId}`)
        }
      }

      // Create socket connection
      const socket = makeWASocket({
        auth: state,
        logger,
        browser: ['Bayles WhatsApp', 'Chrome', '1.0.0'],
      })

      // Create connection object
      const connection: BaileysConnection = {
        socket,
        sessionId,
        whatsappNumberId,
        qrCode: null,
        state: 'connecting',
      }

      // Store the connection
      this.connections.set(sessionId, connection)

      // Set up connection event handlers
      this.setupConnectionHandlers(connection, customSaveCreds)

      // Return a promise that resolves when either connected or QR code is ready
      return new Promise((resolve, reject) => {
        let resolved = false

        // Listen for connection updates
        socket.ev.on('connection.update', (update) => {
          const { connection: connectionState, lastDisconnect, qr } = update

          // Handle successful connection (no QR code needed)
          if (connectionState === 'open' && !resolved) {
            resolved = true
            logger.info(`Connection established for session ${sessionId} without QR code`)
            resolve('') // Empty string indicates no QR code was needed
          }

          // Handle QR code generation
          if (qr && !resolved) {
            logger.info(`QR code generated for session ${sessionId}`)

            // Update connection object
            const conn = this.connections.get(sessionId)
            if (conn) {
              conn.qrCode = qr
              conn.state = 'qr_ready'
            }

            // Only resolve with QR code if this is not a reconnection attempt
            if (!isReconnection) {
              resolved = true
              resolve(qr)
            }
          }

          // Handle connection failures
          if (connectionState === 'close' && !resolved) {
            const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode

            // If logged out, we need a new QR code
            if (statusCode === DisconnectReason.loggedOut) {
              logger.info(`Session ${sessionId} was logged out, QR code required`)
              if (isReconnection) {
                resolved = true
                reject(new Error('Session logged out, QR code required'))
              }
            }
          }
        })

        // Set a timeout for connection attempts
        setTimeout(() => {
          if (!resolved) {
            const conn = this.connections.get(sessionId)
            if (conn?.qrCode && !isReconnection) {
              // QR code was generated, return it
              resolved = true
              resolve(conn.qrCode)
            } else if (isReconnection) {
              // Reconnection failed
              resolved = true
              reject(new Error('Reconnection timeout'))
            } else {
              // No QR code generated
              resolved = true
              logger.warn(`Connection timeout for session ${sessionId}`)
              resolve('')
            }
          }
        }, isReconnection ? 15000 : 30000) // Shorter timeout for reconnections
      })
    } catch (error) {
      logger.error(`Error initializing connection for session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Set up connection event handlers
   * @param connection Connection object
   * @param saveCreds Function to save credentials
   */
  private setupConnectionHandlers(
    connection: BaileysConnection,
    saveCreds: () => Promise<void>
  ): void {
    const { socket, sessionId, whatsappNumberId } = connection

    // Handle connection updates
    socket.ev.on('connection.update', async (update) => {
      const { connection: connectionState, lastDisconnect } = update

      // Update connection object
      const conn = this.connections.get(sessionId)
      if (!conn) return;

      if (connectionState) {
        conn.state = connectionState === 'open' ? 'connected' : 'disconnected'

        if (connectionState === 'open') {
          logger.info(`Connection established for session ${sessionId}`)

          // Get the connected WhatsApp number information
          const phoneNumber = socket.user?.id?.split(':')[0] || ''
          const contactName = socket.user?.name || ''

          logger.info(`Connected WhatsApp: ${phoneNumber} (${contactName})`)

          // Update WhatsApp number in database with actual phone number and name
          await this.updateWhatsappNumber(whatsappNumberId, {
            phoneNumber,
            contactName,
            status: 'active'
          })

          // Save credentials
          await saveCreds()
        } else if (connectionState === 'close') {
          const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode
          const shouldReconnect = statusCode !== DisconnectReason.loggedOut

          conn.lastDisconnectReason = DisconnectReason[statusCode] || 'unknown'

          logger.info(`Connection closed for session ${sessionId}. Status code: ${statusCode}`)

          if (shouldReconnect) {
            logger.info(`Attempting to reconnect session ${sessionId}...`)

            // If the connection was closed due to a restart required, we need to reconnect
            if (statusCode === DisconnectReason.restartRequired) {
              logger.info(`Connection restart required for session ${sessionId}`)
            }

            // Remove the old connection
            this.connections.delete(sessionId)

            // Wait a bit before reconnecting to avoid rapid reconnection attempts
            setTimeout(async () => {
              try {
                // Initialize a new connection (this is a reconnection)
                await this.initializeConnection(sessionId, whatsappNumberId, true)
              } catch (error) {
                logger.error(`Error reconnecting session ${sessionId}:`, error)
                // Mark as inactive if reconnection fails
                await this.updateWhatsappNumberStatus(whatsappNumberId, 'inactive')
              }
            }, 3000); // Wait 3 seconds before reconnecting
          } else {
            // Update WhatsApp number status in database
            await this.updateWhatsappNumberStatus(whatsappNumberId, 'inactive')
          }
        }
      }
    })

    // Handle credentials update
    socket.ev.on('creds.update', async () => {
      logger.info(`Credentials updated for session ${sessionId}`)
      await saveCreds()
    })
  }

  /**
   * Update WhatsApp number status in database
   * @param whatsappNumberId WhatsApp number ID
   * @param status New status
   */
  private async updateWhatsappNumberStatus(
    whatsappNumberId: number,
    status: 'active' | 'inactive' | 'pending'
  ): Promise<void> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
      if (whatsappNumber) {
        whatsappNumber.status = status

        // Update lastConnected timestamp if status is active
        if (status === 'active') {
          whatsappNumber.lastConnected = DateTime.now()
        }

        await whatsappNumber.save()
      }
    } catch (error) {
      logger.error(`Error updating WhatsApp number status:`, error)
    }
  }

  /**
   * Update WhatsApp number in database with actual information
   * @param whatsappNumberId WhatsApp number ID
   * @param data Update data
   */
  private async updateWhatsappNumber(
    whatsappNumberId: number,
    data: {
      phoneNumber?: string
      contactName?: string
      status?: 'active' | 'inactive' | 'pending'
    }
  ): Promise<void> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
      if (whatsappNumber) {
        // Only update phone number if it's provided and the current one is a placeholder
        if (data.phoneNumber && (whatsappNumber.phoneNumber === '0000000000' || whatsappNumber.phoneNumber.startsWith('temp-'))) {
          whatsappNumber.phoneNumber = data.phoneNumber
        }

        // Update contact name if provided
        if (data.contactName) {
          whatsappNumber.contactName = data.contactName
        }

        // Update status if provided
        if (data.status) {
          whatsappNumber.status = data.status
        }

        // Always update lastConnected timestamp
        whatsappNumber.lastConnected = DateTime.now()

        await whatsappNumber.save()
        logger.info(`Updated WhatsApp number ${whatsappNumberId} with actual information`)
      }
    } catch (error) {
      logger.error(`Error updating WhatsApp number:`, error)
    }
  }

  /**
   * Get all active connections
   */
  public getConnections(): BaileysConnection[] {
    return Array.from(this.connections.values())
  }

  /**
   * Get a specific connection
   * @param sessionId Session ID
   */
  public getConnection(sessionId: string): BaileysConnection | undefined {
    return this.connections.get(sessionId)
  }

  /**
   * Auto-reconnect all active WhatsApp numbers on startup
   */
  public async autoReconnectWhatsAppNumbers(): Promise<void> {
    try {
      logger.info('Starting auto-reconnection of WhatsApp numbers...')

      // Get all active WhatsApp numbers that were connected in the last 7 days
      const cutoffDate = DateTime.now().minus({ days: 7 })

      const whatsappNumbers = await WhatsappNumber.query()
        .where('status', 'active')
        .where('last_connected', '>', cutoffDate.toSQL())
        .whereNotNull('session_data')

      logger.info(`Found ${whatsappNumbers.length} WhatsApp numbers to reconnect`)

      // Reconnect each number with a delay to avoid overwhelming the system
      for (let i = 0; i < whatsappNumbers.length; i++) {
        const whatsappNumber = whatsappNumbers[i]

        try {
          // Parse session data to get session ID
          const sessionData = JSON.parse(whatsappNumber.sessionData || '{}')
          const sessionId = sessionData.sessionId || whatsappNumber.qrCodeId

          if (sessionId) {
            logger.info(`Attempting to reconnect WhatsApp number ${whatsappNumber.id} (${whatsappNumber.phoneNumber})`)

            // Add a delay between reconnections to avoid rate limiting
            if (i > 0) {
              await new Promise(resolve => setTimeout(resolve, 2000)) // 2 second delay
            }

            // Attempt to reconnect
            await this.initializeConnection(sessionId, whatsappNumber.id, true)
          } else {
            logger.warn(`No session ID found for WhatsApp number ${whatsappNumber.id}`)
          }
        } catch (error) {
          logger.error(`Error reconnecting WhatsApp number ${whatsappNumber.id}:`, error)

          // Mark as inactive if reconnection fails
          whatsappNumber.status = 'inactive'
          await whatsappNumber.save()
        }
      }

      logger.info('Auto-reconnection process completed')
    } catch (error) {
      logger.error('Error during auto-reconnection:', error)
    }
  }

  /**
   * Check if a session has valid credentials for reconnection
   * @param sessionId Session ID
   */
  private async hasValidSession(sessionId: string): Promise<boolean> {
    try {
      const sessionDir = this.getSessionPath(sessionId)
      const credsPath = path.join(sessionDir, 'creds.json')

      // Check if credentials file exists and is not empty
      if (fs.existsSync(credsPath)) {
        const stats = fs.statSync(credsPath)
        return stats.size > 0
      }

      return false
    } catch (error) {
      logger.error(`Error checking session validity for ${sessionId}:`, error)
      return false
    }
  }
}

// Export singleton instance
export default BaileysService.getInstance()
