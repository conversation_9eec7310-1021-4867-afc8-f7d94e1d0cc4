import { makeWASocket, useMultiFileAuthState, DisconnectReason } from '@whiskeysockets/baileys'
import { Boom } from '@hapi/boom'
import { EventEmitter } from 'node:events'
import fs from 'node:fs'
import path from 'node:path'
import WhatsappNumber from '#models/whatsapp_number'
import app from '@adonisjs/core/services/app'

/**
 * WhatsApp service for managing WhatsApp connections using Baileys
 */
export class WhatsappService {
  private static instance: WhatsappService
  private connections: Map<string, any> = new Map()
  private qrCodes: Map<string, string> = new Map()
  private eventEmitter = new EventEmitter()

  /**
   * Get the singleton instance of WhatsappService
   */
  public static getInstance(): WhatsappService {
    if (!WhatsappService.instance) {
      WhatsappService.instance = new WhatsappService()
    }
    return WhatsappService.instance
  }

  /**
   * Initialize a new WhatsApp connection
   * @param sessionId Unique session ID for this connection
   * @param whatsappNumberId ID of the WhatsApp number in the database
   */
  public async initializeConnection(sessionId: string, whatsappNumberId: number): Promise<string> {
    try {
      // Create sessions directory if it doesn't exist
      const sessionsDir = path.join(app.makePath('tmp'), 'sessions', sessionId)
      if (!fs.existsSync(sessionsDir)) {
        fs.mkdirSync(sessionsDir, { recursive: true })
      }

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionsDir)

      // Create WhatsApp socket connection
      const sock = makeWASocket({
        printQRInTerminal: true,
        auth: state,
        browser: ['Bayles WhatsApp', 'Chrome', '1.0.0'],
      })

      // Store the connection
      this.connections.set(sessionId, sock)

      // Handle connection updates
      sock.ev.on('connection.update', async (update) => {
        const { connection, lastDisconnect, qr } = update

        // If QR code is received, store it
        if (qr) {
          this.qrCodes.set(sessionId, qr)
          this.eventEmitter.emit(`qr-${sessionId}`, qr)
        }

        // If connection is opened (successful)
        if (connection === 'open') {
          console.log(`WhatsApp connection established for session ${sessionId}`)

          // Update WhatsApp number status in database
          const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
          if (whatsappNumber) {
            whatsappNumber.status = 'active'
            await whatsappNumber.save()
          }

          // Save credentials
          await saveCreds()

          this.eventEmitter.emit(`connected-${sessionId}`, true)
        }

        // If connection is closed
        if (connection === 'close') {
          const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode
          const shouldReconnect = statusCode !== DisconnectReason.loggedOut

          console.log(`WhatsApp connection closed for session ${sessionId}. Status code: ${statusCode}`)

          if (shouldReconnect) {
            console.log(`Attempting to reconnect session ${sessionId}...`)
            // Reconnect by initializing a new connection
            this.initializeConnection(sessionId, whatsappNumberId)
          } else {
            // Update WhatsApp number status in database
            const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
            if (whatsappNumber) {
              whatsappNumber.status = 'inactive'
              await whatsappNumber.save()
            }

            this.eventEmitter.emit(`disconnected-${sessionId}`, true)
          }
        }
      })

      // Wait for QR code to be generated
      return new Promise((resolve) => {
        // If QR code is already available, resolve immediately
        const existingQR = this.qrCodes.get(sessionId)
        if (existingQR) {
          resolve(existingQR)
          return
        }

        // Otherwise, wait for QR event
        this.eventEmitter.once(`qr-${sessionId}`, (qr) => {
          resolve(qr)
        })

        // Set a timeout in case QR code is not generated
        setTimeout(() => {
          if (!this.qrCodes.has(sessionId)) {
            resolve('')
          }
        }, 30000) // 30 seconds timeout
      })
    } catch (error) {
      console.error(`Error initializing WhatsApp connection for session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Get the QR code for a session
   * @param sessionId Session ID
   */
  public getQRCode(sessionId: string): string | null {
    return this.qrCodes.get(sessionId) || null
  }

  /**
   * Close a WhatsApp connection
   * @param sessionId Session ID
   */
  public async closeConnection(sessionId: string): Promise<boolean> {
    try {
      const sock = this.connections.get(sessionId)
      if (sock) {
        // Logout and close connection
        await sock.logout()
        this.connections.delete(sessionId)
        this.qrCodes.delete(sessionId)
        return true
      }
      return false
    } catch (error) {
      console.error(`Error closing WhatsApp connection for session ${sessionId}:`, error)
      return false
    }
  }
}

// Export singleton instance
export default WhatsappService.getInstance()
