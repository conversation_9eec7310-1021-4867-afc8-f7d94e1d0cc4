import app from '@adonisjs/core/services/app'
import type { Client } from '@libsql/client'

/**
 * Service for interacting with Turso database directly
 */
export class TursoService {
  /**
   * Get the Turso client
   */
  static getClient(): Client {
    return app.container.resolve('turso') as Client
  }

  /**
   * Execute a query
   */
  static async query(sql: string, params: any[] = []): Promise<any> {
    const client = this.getClient()
    const result = await client.execute({ sql, args: params })
    return result
  }

  /**
   * Execute a query and return the first row
   */
  static async queryFirst(sql: string, params: any[] = []): Promise<any> {
    const result = await this.query(sql, params)
    return result.rows[0] || null
  }

  /**
   * Execute a query and return all rows
   */
  static async queryAll(sql: string, params: any[] = []): Promise<any[]> {
    const result = await this.query(sql, params)
    return result.rows
  }

  /**
   * Execute a query and return the number of affected rows
   */
  static async execute(sql: string, params: any[] = []): Promise<number> {
    const result = await this.query(sql, params)
    return result.rowsAffected
  }
}
