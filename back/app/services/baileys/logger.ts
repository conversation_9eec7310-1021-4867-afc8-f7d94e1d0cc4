/**
 * Custom logger for <PERSON><PERSON>
 */
import pino from 'pino'
import app from '@adonisjs/core/services/app'
import path from 'node:path'
import fs from 'node:fs'

// Create logs directory if it doesn't exist
const logsDir = path.join(app.makePath('tmp'), 'logs')
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true })
}

// Create a logger instance
const logger = pino({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
    },
  },
  timestamp: () => `,"time":"${new Date().toISOString()}"`,
}, pino.destination(path.join(logsDir, 'baileys.log')))

// Export the logger
export default logger
