/**
 * Session Manager for <PERSON>s
 *
 * Handles session storage, retrieval, and cleanup
 */

import fs from 'node:fs'
import path from 'node:path'
import app from '@adonisjs/core/services/app'
import logger from './logger'
import WhatsappNumber from '#models/whatsapp_number'
import { DateTime } from 'luxon'

export class SessionManager {
  private readonly sessionsDir: string

  constructor() {
    // Set up sessions directory
    this.sessionsDir = path.join(app.makePath('tmp'), 'baileys_sessions')
    this.ensureSessionsDirectory()
  }

  /**
   * Ensure the sessions directory exists
   */
  private ensureSessionsDirectory(): void {
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true })
      logger.info(`Created sessions directory at ${this.sessionsDir}`)
    }
  }

  /**
   * Get the path for a specific session
   * @param sessionId Session ID
   */
  public getSessionPath(sessionId: string): string {
    const sessionPath = path.join(this.sessionsDir, sessionId)

    // Create session directory if it doesn't exist
    if (!fs.existsSync(sessionPath)) {
      fs.mkdirSync(sessionPath, { recursive: true })
    }

    return sessionPath
  }

  /**
   * Check if a session exists
   * @param sessionId Session ID
   */
  public sessionExists(sessionId: string): boolean {
    const sessionPath = path.join(this.sessionsDir, sessionId)
    return fs.existsSync(sessionPath) && fs.readdirSync(sessionPath).length > 0
  }

  /**
   * Save session data to the database
   * @param whatsappNumberId WhatsApp number ID
   * @param sessionData Session data to save
   */
  public async saveSessionData(whatsappNumberId: number, sessionData: any): Promise<boolean> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)

      if (!whatsappNumber) {
        logger.error(`WhatsApp number with ID ${whatsappNumberId} not found`)
        return false
      }

      // Serialize session data
      const serializedData = JSON.stringify(sessionData)

      // Update WhatsApp number with session data
      whatsappNumber.sessionData = serializedData
      whatsappNumber.lastConnected = DateTime.now()
      await whatsappNumber.save()

      logger.info(`Saved session data for WhatsApp number ${whatsappNumberId}`)
      return true
    } catch (error) {
      logger.error(`Error saving session data for WhatsApp number ${whatsappNumberId}:`, error)
      return false
    }
  }

  /**
   * Get session data from the database
   * @param whatsappNumberId WhatsApp number ID
   */
  public async getSessionData(whatsappNumberId: number): Promise<any | null> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)

      if (!whatsappNumber || !whatsappNumber.sessionData) {
        return null
      }

      // Deserialize session data
      return JSON.parse(whatsappNumber.sessionData)
    } catch (error) {
      logger.error(`Error getting session data for WhatsApp number ${whatsappNumberId}:`, error)
      return null
    }
  }

  /**
   * Clear session data from the database
   * @param whatsappNumberId WhatsApp number ID
   */
  public async clearSessionData(whatsappNumberId: number): Promise<boolean> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)

      if (!whatsappNumber) {
        return false
      }

      // Clear session data
      whatsappNumber.sessionData = null
      await whatsappNumber.save()

      logger.info(`Cleared session data for WhatsApp number ${whatsappNumberId}`)
      return true
    } catch (error) {
      logger.error(`Error clearing session data for WhatsApp number ${whatsappNumberId}:`, error)
      return false
    }
  }

  /**
   * Delete a session
   * @param sessionId Session ID
   */
  public deleteSession(sessionId: string): boolean {
    try {
      const sessionPath = path.join(this.sessionsDir, sessionId)

      if (fs.existsSync(sessionPath)) {
        // Recursively delete the directory
        fs.rmSync(sessionPath, { recursive: true, force: true })
        logger.info(`Deleted session ${sessionId}`)
        return true
      }

      return false
    } catch (error) {
      logger.error(`Error deleting session ${sessionId}:`, error)
      return false
    }
  }

  /**
   * List all sessions
   */
  public listSessions(): string[] {
    try {
      return fs.readdirSync(this.sessionsDir)
        .filter(item => {
          const itemPath = path.join(this.sessionsDir, item)
          return fs.statSync(itemPath).isDirectory() && fs.readdirSync(itemPath).length > 0
        })
    } catch (error) {
      logger.error('Error listing sessions:', error)
      return []
    }
  }

  /**
   * Clean up old sessions
   * @param maxAge Maximum age in milliseconds
   */
  public cleanupOldSessions(maxAge: number = 7 * 24 * 60 * 60 * 1000): void {
    try {
      const now = Date.now()
      const sessions = this.listSessions()

      for (const sessionId of sessions) {
        const sessionPath = path.join(this.sessionsDir, sessionId)
        const stats = fs.statSync(sessionPath)

        // If the session is older than maxAge, delete it
        if (now - stats.mtimeMs > maxAge) {
          this.deleteSession(sessionId)
          logger.info(`Cleaned up old session ${sessionId}`)
        }
      }
    } catch (error) {
      logger.error('Error cleaning up old sessions:', error)
    }
  }
}
