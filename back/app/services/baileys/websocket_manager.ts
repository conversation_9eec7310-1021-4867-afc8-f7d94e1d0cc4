/**
 * WebSocket Manager for Baileys
 *
 * Handles WebSocket connections for real-time updates
 */

import { Server as SocketServer } from 'socket.io'
import { Server as HttpServer } from 'node:http'
import { EventEmitter } from 'node:events'
import logger from './logger'

export class WebSocketManager {
  private io: SocketServer | null = null
  private eventEmitter: EventEmitter
  private connectedClients: Map<string, Set<string>> = new Map()

  /**
   * Constructor
   * @param eventEmitter Event emitter for Baileys events
   */
  constructor(eventEmitter: EventEmitter) {
    this.eventEmitter = eventEmitter
  }

  /**
   * Initialize the WebSocket server
   * @param server HTTP server
   */
  public initialize(server: HttpServer): void {
    if (this.io) {
      return
    }

    // Create Socket.IO server
    this.io = new SocketServer(server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST']
      }
    })

    // Set up connection handler
    this.io.on('connection', (socket) => {
      logger.info(`WebSocket client connected: ${socket.id}`)

      // Handle session subscription
      socket.on('subscribe', (sessionId: string) => {
        this.subscribeToSession(socket.id, sessionId)
        socket.join(`session:${sessionId}`)
        logger.info(`Client ${socket.id} subscribed to session ${sessionId}`)
      })

      // Handle session unsubscription
      socket.on('unsubscribe', (sessionId: string) => {
        this.unsubscribeFromSession(socket.id, sessionId)
        socket.leave(`session:${sessionId}`)
        logger.info(`Client ${socket.id} unsubscribed from session ${sessionId}`)
      })

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleClientDisconnect(socket.id)
        logger.info(`WebSocket client disconnected: ${socket.id}`)
      })
    })

    logger.info('WebSocket server initialized')
  }

  /**
   * Subscribe a client to a session
   * @param clientId Client ID
   * @param sessionId Session ID
   */
  private subscribeToSession(clientId: string, sessionId: string): void {
    if (!this.connectedClients.has(sessionId)) {
      this.connectedClients.set(sessionId, new Set())
    }

    this.connectedClients.get(sessionId)!.add(clientId)
  }

  /**
   * Unsubscribe a client from a session
   * @param clientId Client ID
   * @param sessionId Session ID
   */
  private unsubscribeFromSession(clientId: string, sessionId: string): void {
    const clients = this.connectedClients.get(sessionId)

    if (clients) {
      clients.delete(clientId)

      if (clients.size === 0) {
        this.connectedClients.delete(sessionId)
      }
    }
  }

  /**
   * Handle client disconnection
   * @param clientId Client ID
   */
  private handleClientDisconnect(clientId: string): void {
    // Remove client from all sessions
    for (const [sessionId, clients] of this.connectedClients.entries()) {
      if (clients.has(clientId)) {
        clients.delete(clientId)

        if (clients.size === 0) {
          this.connectedClients.delete(sessionId)
        }
      }
    }
  }

  /**
   * Broadcast a connection update
   * @param data Update data
   */
  public broadcastConnectionUpdate(data: any): void {
    if (!this.io) {
      logger.warn('WebSocket server not initialized')
      return
    }

    const { sessionId } = data

    if (sessionId) {
      this.io.to(`session:${sessionId}`).emit('connection.update', data)
      logger.debug(`Broadcasted connection update for session ${sessionId}`)
    }
  }

  /**
   * Broadcast a message update
   * @param data Message data
   */
  public broadcastMessageUpdate(data: any): void {
    if (!this.io) {
      logger.warn('WebSocket server not initialized')
      return
    }

    const { sessionId } = data

    if (sessionId) {
      this.io.to(`session:${sessionId}`).emit('message.update', data)
      logger.debug(`Broadcasted message update for session ${sessionId}`)
    }
  }

  /**
   * Broadcast a QR code update
   * @param sessionId Session ID
   * @param qrCode QR code data
   */
  public broadcastQRCode(sessionId: string, qrCode: string): void {
    if (!this.io) {
      logger.warn('WebSocket server not initialized')
      return
    }

    this.io.to(`session:${sessionId}`).emit('qr.update', {
      sessionId,
      qrCode
    })

    logger.debug(`Broadcasted QR code for session ${sessionId}`)
  }

  /**
   * Get the number of connected clients for a session
   * @param sessionId Session ID
   */
  public getConnectedClientsCount(sessionId: string): number {
    const clients = this.connectedClients.get(sessionId)
    return clients ? clients.size : 0
  }

  /**
   * Check if a session has any connected clients
   * @param sessionId Session ID
   */
  public hasConnectedClients(sessionId: string): boolean {
    return this.getConnectedClientsCount(sessionId) > 0
  }
}
