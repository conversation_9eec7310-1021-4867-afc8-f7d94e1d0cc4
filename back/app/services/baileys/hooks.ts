/**
 * Hooks for <PERSON><PERSON>
 *
 * Provides easy-to-use hooks for interacting with the Baileys service
 */

import BaileysService, { BaileysConnection } from './index'
import WhatsappNumber from '#models/whatsapp_number'
import { DateTime } from 'luxon'
import logger from './logger'

/**
 * Initialize a WhatsApp connection and get a QR code
 * @param whatsappNumberId WhatsApp number ID
 */
export async function useWhatsAppQRCode(whatsappNumberId: number): Promise<{
  qrCodeUrl: string;
  sessionId: string;
}> {
  try {
    // Get the WhatsApp number
    const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)

    if (!whatsappNumber) {
      throw new Error(`WhatsApp number with ID ${whatsappNumberId} not found`)
    }

    // Generate a session ID if not already present
    const sessionId = whatsappNumber.qrCodeId || `session_${Date.now()}_${whatsappNumberId}`

    // Update the WhatsApp number with the session ID
    if (!whatsappNumber.qrCodeId) {
      whatsappNumber.qrCodeId = sessionId
      await whatsappNumber.save()
    }

    // Initialize the connection and get the QR code
    const qrCode = await BaileysService.initializeConnection(sessionId, whatsappNumberId)

    if (!qrCode) {
      throw new Error('Failed to generate QR code')
    }

    // Generate a QR code URL
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrCode)}`

    return {
      qrCodeUrl,
      sessionId
    }
  } catch (error) {
    logger.error('Error in useWhatsAppQRCode:', error)
    throw error
  }
}

/**
 * Send a text message
 * @param sessionId Session ID
 * @param to Recipient phone number (with country code, no + or spaces)
 * @param text Message text
 */
export async function useSendTextMessage(sessionId: string, to: string, text: string): Promise<any> {
  try {
    // Format the recipient number
    const formattedTo = `${to.replace(/\D/g, '')}@s.whatsapp.net`

    // Send the message
    return await BaileysService.sendTextMessage(sessionId, formattedTo, text)
  } catch (error) {
    logger.error('Error in useSendTextMessage:', error)
    throw error
  }
}

/**
 * Get connection status
 * @param sessionId Session ID
 */
export function useConnectionStatus(sessionId: string): {
  isConnected: boolean;
  state: string;
  connection?: BaileysConnection;
} {
  try {
    const connection = BaileysService.getConnection(sessionId)

    return {
      isConnected: connection?.state === 'connected',
      state: connection?.state || 'disconnected',
      connection
    }
  } catch (error) {
    logger.error('Error in useConnectionStatus:', error)
    return {
      isConnected: false,
      state: 'error'
    }
  }
}

/**
 * Close a WhatsApp connection
 * @param sessionId Session ID
 */
export async function useCloseConnection(sessionId: string): Promise<boolean> {
  try {
    return await BaileysService.closeConnection(sessionId)
  } catch (error) {
    logger.error('Error in useCloseConnection:', error)
    return false
  }
}

/**
 * Get all active connections
 */
export function useAllConnections(): BaileysConnection[] {
  try {
    return BaileysService.getConnections()
  } catch (error) {
    logger.error('Error in useAllConnections:', error)
    return []
  }
}

/**
 * Get connection by WhatsApp number ID
 * @param whatsappNumberId WhatsApp number ID
 */
export async function useConnectionByWhatsAppNumber(whatsappNumberId: number): Promise<BaileysConnection | null> {
  try {
    // Get the WhatsApp number
    const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)

    if (!whatsappNumber || !whatsappNumber.qrCodeId) {
      return null
    }

    // Get the connection
    const connection = BaileysService.getConnection(whatsappNumber.qrCodeId)

    return connection || null
  } catch (error) {
    logger.error('Error in useConnectionByWhatsAppNumber:', error)
    return null
  }
}

/**
 * Initialize WebSocket server
 * @param server HTTP server
 */
export function useInitializeWebSockets(server: any): void {
  try {
    // Get the WebSocket manager from the Baileys service
    const webSocketManager = (BaileysService as any).webSocketManager

    if (webSocketManager) {
      webSocketManager.initialize(server)
    }
  } catch (error) {
    logger.error('Error in useInitializeWebSockets:', error)
  }
}

/**
 * Auto-reconnect all active WhatsApp numbers
 * This function can be called on application startup to reconnect all previously active numbers
 */
export async function useAutoReconnectWhatsAppNumbers(): Promise<void> {
  try {
    logger.info('Auto-reconnecting WhatsApp numbers...')

    // Get all WhatsApp numbers with session data
    const whatsappNumbers = await WhatsappNumber.query()
      .whereNotNull('session_data')
      .where('status', 'active')
      .exec()

    if (whatsappNumbers.length === 0) {
      logger.info('No active WhatsApp numbers found for auto-reconnection')
      return
    }

    logger.info(`Found ${whatsappNumbers.length} active WhatsApp numbers to reconnect`)

    // Reconnect each number
    for (const whatsappNumber of whatsappNumbers) {
      try {
        // Check if the number was connected recently (within the last 7 days)
        const lastConnected = whatsappNumber.lastConnected
        const now = DateTime.now()
        const daysSinceLastConnection = lastConnected ? now.diff(lastConnected, 'days').days : 999

        // Only reconnect if the number was connected within the last 7 days
        if (daysSinceLastConnection <= 7) {
          logger.info(`Auto-reconnecting WhatsApp number ${whatsappNumber.id} (${whatsappNumber.phoneNumber})`)

          // Initialize connection
          await BaileysService.initializeConnection(
            whatsappNumber.qrCodeId || `session_${Date.now()}_${whatsappNumber.id}`,
            whatsappNumber.id
          )
        } else {
          logger.info(`Skipping auto-reconnect for WhatsApp number ${whatsappNumber.id} - last connected ${daysSinceLastConnection} days ago`)
        }
      } catch (error) {
        logger.error(`Error auto-reconnecting WhatsApp number ${whatsappNumber.id}:`, error)
      }
    }

    logger.info('Auto-reconnection process completed')
  } catch (error) {
    logger.error('Error in useAutoReconnectWhatsAppNumbers:', error)
  }
}
