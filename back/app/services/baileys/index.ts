/**
 * <PERSON>s Module for Bayles
 *
 * This module provides a comprehensive interface for working with the Baileys WhatsApp library.
 * It handles connection management, session persistence, message handling, and WebSocket integration.
 */

import {
  makeWASocket,
  useMultiFileAuthState,
  DisconnectReason,
  WASocket,
  ConnectionState,
  WAMessageKey,
  proto,
  downloadMediaMessage,
  isJidGroup,
  isJidUser,
  jidNormalizedUser,
} from '@whiskeysockets/baileys'
import { Boom } from '@hapi/boom'
import { EventEmitter } from 'node:events'
import fs from 'node:fs'
import path from 'node:path'
import app from '@adonisjs/core/services/app'
import WhatsappNumber from '#models/whatsapp_number'
import Message from '#models/message'
import { DateTime } from 'luxon'
import logger from './logger'
import { SessionManager } from './session_manager'
import { MessageHandler } from './message_handler'
import { WebSocketManager } from './websocket_manager'

// Types
export interface BaileysConnection {
  socket: WASocket
  sessionId: string
  whatsappNumberId: number
  qrCode: string | null
  state: 'connecting' | 'connected' | 'disconnected' | 'qr_ready'
  lastDisconnectReason?: string
}

/**
 * Main Baileys service class
 */
export class BaileysService {
  private static instance: BaileysService
  private connections: Map<string, BaileysConnection> = new Map()
  private eventEmitter = new EventEmitter()
  private sessionManager: SessionManager
  private messageHandler: MessageHandler
  private webSocketManager: WebSocketManager

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.sessionManager = new SessionManager()
    this.messageHandler = new MessageHandler()
    this.webSocketManager = new WebSocketManager(this.eventEmitter)

    // Set up event listeners
    this.setupEventListeners()

    // Initialize sessions directory
    this.initializeSessionsDirectory()

    logger.info('BaileysService initialized')
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): BaileysService {
    if (!BaileysService.instance) {
      BaileysService.instance = new BaileysService()
    }
    return BaileysService.instance
  }

  /**
   * Initialize the sessions directory
   */
  private initializeSessionsDirectory(): void {
    const sessionsDir = path.join(app.makePath('tmp'), 'baileys_sessions')
    if (!fs.existsSync(sessionsDir)) {
      fs.mkdirSync(sessionsDir, { recursive: true })
    }
  }

  /**
   * Set up event listeners
   */
  private setupEventListeners(): void {
    // Listen for new messages
    this.eventEmitter.on('message.new', (data) => {
      this.messageHandler.handleIncomingMessage(data)
    })

    // Listen for connection status changes
    this.eventEmitter.on('connection.update', (data) => {
      this.webSocketManager.broadcastConnectionUpdate(data)
    })
  }

  /**
   * Initialize a new WhatsApp connection
   * @param sessionId Unique session ID
   * @param whatsappNumberId WhatsApp number ID in the database
   */
  public async initializeConnection(sessionId: string, whatsappNumberId: number): Promise<string> {
    try {
      logger.info(`Initializing connection for session ${sessionId}`)

      // Get the session directory
      const sessionDir = this.sessionManager.getSessionPath(sessionId)

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir)

      // Create a custom saveCreds function that also saves to the database
      const customSaveCreds = async () => {
        // Save credentials to file system
        await saveCreds()

        // Save session data to the database
        await this.sessionManager.saveSessionData(whatsappNumberId, {
          sessionId,
          lastConnected: new Date().toISOString(),
        })

        logger.info(`Saved credentials for WhatsApp number ${whatsappNumberId}`)
      }

      // Create socket connection
      const socket = makeWASocket({
        printQRInTerminal: true,
        auth: state,
        logger,
        browser: ['Bayles WhatsApp', 'Chrome', '1.0.0'],
      })

      // Create connection object
      const connection: BaileysConnection = {
        socket,
        sessionId,
        whatsappNumberId,
        qrCode: null,
        state: 'connecting',
      }

      // Store the connection
      this.connections.set(sessionId, connection)

      // Set up connection event handlers
      this.setupConnectionHandlers(connection, customSaveCreds)

      // Check if we have existing session data in the database
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)

      if (whatsappNumber?.sessionData && this.sessionManager.sessionExists(sessionId)) {
        logger.info(`Found existing session data for WhatsApp number ${whatsappNumberId}`)

        // If we have session data, we might be able to reconnect without a QR code
        // Return a promise that resolves when either:
        // 1. The connection is established (no QR code needed)
        // 2. A QR code is generated (reconnection failed)
        return new Promise((resolve) => {
          // Listen for connection established event
          this.eventEmitter.once(`connection.update`, (data) => {
            if (data.sessionId === sessionId && data.state === 'connected') {
              logger.info(`Connection established for session ${sessionId} without QR code`)
              resolve('')
            }
          })

          // Listen for QR code event
          this.eventEmitter.once(`qr.${sessionId}`, (qrCode) => {
            logger.info(`QR code generated for session ${sessionId}`)
            resolve(qrCode)
          })

          // Set a timeout in case neither event is triggered
          setTimeout(() => {
            if (!this.connections.get(sessionId)?.qrCode &&
                this.connections.get(sessionId)?.state !== 'connected') {
              logger.warn(`Connection/QR code generation timed out for session ${sessionId}`)
              resolve('')
            }
          }, 30000) // 30 seconds timeout
        })
      } else {
        // No existing session data, we need a QR code
        logger.info(`No existing session data for WhatsApp number ${whatsappNumberId}, generating QR code`)

        // Return a promise that resolves when QR code is ready
        return new Promise((resolve) => {
          this.eventEmitter.once(`qr.${sessionId}`, (qrCode) => {
            resolve(qrCode)
          })

          // Set a timeout in case QR code is not generated
          setTimeout(() => {
            if (!this.connections.get(sessionId)?.qrCode) {
              logger.warn(`QR code generation timed out for session ${sessionId}`)
              resolve('')
            }
          }, 30000) // 30 seconds timeout
        })
      }
    } catch (error) {
      logger.error(`Error initializing connection for session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Set up connection event handlers
   * @param connection Connection object
   * @param saveCreds Function to save credentials
   */
  private setupConnectionHandlers(
    connection: BaileysConnection,
    saveCreds: () => Promise<void>
  ): void {
    const { socket, sessionId, whatsappNumberId } = connection

    // Handle connection updates
    socket.ev.on('connection.update', async (update) => {
      const { connection: connectionState, lastDisconnect, qr } = update

      // Update connection object
      const conn = this.connections.get(sessionId)
      if (conn) {
        // Handle QR code
        if (qr) {
          conn.qrCode = qr
          conn.state = 'qr_ready'
          this.eventEmitter.emit(`qr.${sessionId}`, qr)
          this.eventEmitter.emit('connection.update', {
            sessionId,
            state: 'qr_ready',
            qrCode: qr,
          })
        }

        // Handle connection state changes
        if (connectionState) {
          conn.state = connectionState === 'open' ? 'connected' : 'disconnected'

          if (connectionState === 'open') {
            logger.info(`Connection established for session ${sessionId}`)

            // Update WhatsApp number status in database
            await this.updateWhatsappNumberStatus(whatsappNumberId, 'active')

            // Save credentials
            await saveCreds()

            // Emit connection events
            this.eventEmitter.emit('connection.update', {
              sessionId,
              state: 'connected',
            })

            // Emit specific connection event for this session
            this.eventEmitter.emit(`connected.${sessionId}`, true)
          } else if (connectionState === 'close') {
            const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode
            const shouldReconnect = statusCode !== DisconnectReason.loggedOut

            conn.lastDisconnectReason = DisconnectReason[statusCode] || 'unknown'

            logger.info(`Connection closed for session ${sessionId}. Status code: ${statusCode}`)

            if (shouldReconnect) {
              logger.info(`Attempting to reconnect session ${sessionId}...`)
              // We'll handle reconnection in a separate method
              this.reconnectSession(sessionId, whatsappNumberId)
            } else {
              // Update WhatsApp number status in database
              await this.updateWhatsappNumberStatus(whatsappNumberId, 'inactive')

              // Emit disconnection event
              this.eventEmitter.emit('connection.update', {
                sessionId,
                state: 'disconnected',
                reason: conn.lastDisconnectReason,
              })
            }
          }
        }
      }
    })

    // Handle messages
    socket.ev.on('messages.upsert', async (messagesUpsert) => {
      if (messagesUpsert.type === 'notify') {
        for (const msg of messagesUpsert.messages) {
          if (!msg.key.fromMe) {
            // Process incoming message
            this.eventEmitter.emit('message.new', {
              sessionId,
              whatsappNumberId,
              message: msg,
            })
          }
        }
      }
    })
  }

  /**
   * Reconnect a session
   * @param sessionId Session ID
   * @param whatsappNumberId WhatsApp number ID
   */
  private async reconnectSession(sessionId: string, whatsappNumberId: number): Promise<void> {
    try {
      // Remove the old connection
      this.connections.delete(sessionId)

      // Initialize a new connection
      await this.initializeConnection(sessionId, whatsappNumberId)
    } catch (error) {
      logger.error(`Error reconnecting session ${sessionId}:`, error)
    }
  }

  /**
   * Update WhatsApp number status in database
   * @param whatsappNumberId WhatsApp number ID
   * @param status New status
   */
  private async updateWhatsappNumberStatus(
    whatsappNumberId: number,
    status: 'active' | 'inactive' | 'pending'
  ): Promise<void> {
    try {
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
      if (whatsappNumber) {
        whatsappNumber.status = status

        // Update lastConnected timestamp if status is active
        if (status === 'active') {
          whatsappNumber.lastConnected = DateTime.now()
        }

        await whatsappNumber.save()
      }
    } catch (error) {
      logger.error(`Error updating WhatsApp number status:`, error)
    }
  }

  /**
   * Send a text message
   * @param sessionId Session ID
   * @param to Recipient JID
   * @param text Message text
   */
  public async sendTextMessage(sessionId: string, to: string, text: string): Promise<any> {
    const connection = this.connections.get(sessionId)
    if (!connection || connection.state !== 'connected') {
      throw new Error(`No active connection for session ${sessionId}`)
    }

    return await connection.socket.sendMessage(to, { text })
  }

  /**
   * Close a connection
   * @param sessionId Session ID
   */
  public async closeConnection(sessionId: string): Promise<boolean> {
    const connection = this.connections.get(sessionId)
    if (!connection) {
      return false
    }

    try {
      // Logout from WhatsApp
      await connection.socket.logout()

      // Remove connection
      this.connections.delete(sessionId)

      // Update WhatsApp number status
      await this.updateWhatsappNumberStatus(connection.whatsappNumberId, 'inactive')

      return true
    } catch (error) {
      logger.error(`Error closing connection for session ${sessionId}:`, error)
      return false
    }
  }

  /**
   * Get all active connections
   */
  public getConnections(): BaileysConnection[] {
    return Array.from(this.connections.values())
  }

  /**
   * Get a specific connection
   * @param sessionId Session ID
   */
  public getConnection(sessionId: string): BaileysConnection | undefined {
    return this.connections.get(sessionId)
  }
}

// Export singleton instance
export default BaileysService.getInstance()
