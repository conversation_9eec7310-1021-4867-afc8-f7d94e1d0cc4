/**
 * Message Handler for <PERSON>s
 *
 * Processes incoming and outgoing messages
 */

import { proto, downloadMediaMessage } from '@whiskeysockets/baileys'
import { DateTime } from 'luxon'
import Message from '#models/message'
import WhatsappNumber from '#models/whatsapp_number'
import logger from './logger'
import fs from 'node:fs'
import path from 'node:path'
import app from '@adonisjs/core/services/app'

// Types
interface MessageData {
  sessionId: string
  whatsappNumberId: number
  message: proto.IWebMessageInfo
}

export class MessageHandler {
  private readonly mediaDir: string

  constructor() {
    // Set up media directory
    this.mediaDir = path.join(app.makePath('tmp'), 'baileys_media')
    this.ensureMediaDirectory()
  }

  /**
   * Ensure the media directory exists
   */
  private ensureMediaDirectory(): void {
    if (!fs.existsSync(this.mediaDir)) {
      fs.mkdirSync(this.mediaDir, { recursive: true })
      logger.info(`Created media directory at ${this.mediaDir}`)
    }
  }

  /**
   * Handle an incoming message
   * @param data Message data
   */
  public async handleIncomingMessage(data: MessageData): Promise<void> {
    try {
      const { sessionId, whatsappNumberId, message } = data

      // Skip messages without content
      if (!message.message) {
        return
      }

      // Get message content
      const messageContent = this.extractMessageContent(message)

      // Skip empty messages
      if (!messageContent) {
        return
      }

      // Get sender information
      const sender = this.extractSender(message)

      // Get WhatsApp number from database
      const whatsappNumber = await WhatsappNumber.find(whatsappNumberId)
      if (!whatsappNumber) {
        logger.warn(`WhatsApp number ${whatsappNumberId} not found for session ${sessionId}`)
        return
      }

      // Create message record
      const messageRecord = new Message()
      messageRecord.senderName = sender.name || 'Unknown'
      messageRecord.senderNumber = sender.jid.split('@')[0]
      messageRecord.receiverName = whatsappNumber.contactName
      messageRecord.receiverNumber = whatsappNumber.phoneNumber
      messageRecord.textMessage = messageContent.text
      messageRecord.projectName = whatsappNumber.projectName
      messageRecord.date = DateTime.now()
      messageRecord.userId = whatsappNumber.userId

      // If there's media, download and save it
      if (messageContent.hasMedia) {
        const mediaPath = await this.downloadMedia(message, sessionId)
        if (mediaPath) {
          messageRecord.mediaUrl = mediaPath
          messageRecord.mediaType = messageContent.mediaType
        }
      }

      // Save the message
      await messageRecord.save()

      logger.info(`Saved incoming message from ${sender.jid} for session ${sessionId}`)
    } catch (error) {
      logger.error('Error handling incoming message:', error)
    }
  }

  /**
   * Extract message content
   * @param message Message object
   */
  private extractMessageContent(message: proto.IWebMessageInfo): {
    text: string
    hasMedia: boolean
    mediaType?: string
  } | null {
    const msg = message.message

    if (!msg) {
      return null
    }

    // Handle different message types
    if (msg.conversation) {
      return {
        text: msg.conversation,
        hasMedia: false
      }
    } else if (msg.extendedTextMessage) {
      return {
        text: msg.extendedTextMessage.text || '',
        hasMedia: false
      }
    } else if (msg.imageMessage) {
      return {
        text: msg.imageMessage.caption || '',
        hasMedia: true,
        mediaType: 'image'
      }
    } else if (msg.videoMessage) {
      return {
        text: msg.videoMessage.caption || '',
        hasMedia: true,
        mediaType: 'video'
      }
    } else if (msg.audioMessage) {
      return {
        text: '[Audio Message]',
        hasMedia: true,
        mediaType: 'audio'
      }
    } else if (msg.documentMessage) {
      return {
        text: msg.documentMessage.fileName || '[Document]',
        hasMedia: true,
        mediaType: 'document'
      }
    } else if (msg.stickerMessage) {
      return {
        text: '[Sticker]',
        hasMedia: true,
        mediaType: 'sticker'
      }
    } else if (msg.contactMessage) {
      return {
        text: `[Contact: ${msg.contactMessage.displayName || 'Unknown'}]`,
        hasMedia: false
      }
    } else if (msg.locationMessage) {
      const { degreesLatitude, degreesLongitude } = msg.locationMessage
      return {
        text: `[Location: ${degreesLatitude}, ${degreesLongitude}]`,
        hasMedia: false
      }
    } else {
      // Unknown message type
      return {
        text: '[Unsupported Message Type]',
        hasMedia: false
      }
    }
  }

  /**
   * Extract sender information
   * @param message Message object
   */
  private extractSender(message: proto.IWebMessageInfo): {
    jid: string
    name?: string
  } {
    const sender = message.key.participant || message.key.remoteJid || ''
    const pushName = message.pushName || undefined

    return {
      jid: sender,
      name: pushName
    }
  }

  /**
   * Download media from a message
   * @param message Message object
   * @param sessionId Session ID
   */
  private async downloadMedia(message: proto.IWebMessageInfo, sessionId: string): Promise<string | null> {
    try {
      // Create session media directory
      const sessionMediaDir = path.join(this.mediaDir, sessionId)
      if (!fs.existsSync(sessionMediaDir)) {
        fs.mkdirSync(sessionMediaDir, { recursive: true })
      }

      // Download the media
      const buffer = await downloadMediaMessage(
        message,
        'buffer',
        {}
      )

      // Generate filename
      const timestamp = Date.now()
      const messageId = message.key.id || 'unknown'
      const extension = this.getFileExtension(message)
      const filename = `${timestamp}-${messageId}.${extension}`
      const filePath = path.join(sessionMediaDir, filename)

      // Save the file
      fs.writeFileSync(filePath, buffer)

      return filePath
    } catch (error) {
      logger.error('Error downloading media:', error)
      return null
    }
  }



  /**
   * Get file extension based on message type
   * @param message Message object
   */
  private getFileExtension(message: proto.IWebMessageInfo): string {
    const msg = message.message

    if (!msg) {
      return 'bin'
    }

    if (msg.imageMessage) {
      return 'jpg'
    } else if (msg.videoMessage) {
      return 'mp4'
    } else if (msg.audioMessage) {
      return msg.audioMessage.ptt ? 'ogg' : 'mp3'
    } else if (msg.documentMessage && msg.documentMessage.fileName) {
      const fileName = msg.documentMessage.fileName
      const lastDotIndex = fileName.lastIndexOf('.')

      if (lastDotIndex !== -1 && lastDotIndex < fileName.length - 1) {
        return fileName.substring(lastDotIndex + 1)
      }

      return 'bin'
    } else if (msg.stickerMessage) {
      return 'webp'
    } else {
      return 'bin'
    }
  }
}
