import { createClient } from '@supabase/supabase-js'
import env from '#start/env'
import { Database } from '../../types/supabase'

// Create a single supabase client for interacting with your database
const supabaseUrl = env.get('SUPABASE_URL')
const supabaseKey = env.get('SUPABASE_KEY')

// Create a single supabase client for interacting with your database
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
  },
})

// Export a function to get a new client with custom auth
export function getSupabaseClient(customToken?: string) {
  return createClient<Database>(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
      ...(customToken ? { global: { headers: { Authorization: `Bearer ${customToken}` } } } : {}),
    },
  })
}
