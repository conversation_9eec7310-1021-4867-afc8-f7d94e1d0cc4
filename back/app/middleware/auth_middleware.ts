import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import type { Authenticators } from '@adonisjs/auth/types'
import { errors } from '@adonisjs/auth'

/**
 * Auth middleware is used authenticate HTTP requests and deny
 * access to unauthenticated users.
 */
export default class AuthMiddleware {
  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    try {
      await ctx.auth.authenticateUsing(options.guards)
      return next()
    } catch (error) {
      if (error instanceof errors.E_UNAUTHORIZED_ACCESS) {
        return ctx.response.unauthorized({
          error: 'Unauthorized access',
          message: 'You must be logged in to access this resource'
        })
      }

      throw error
    }
  }
}