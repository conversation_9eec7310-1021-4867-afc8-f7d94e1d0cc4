export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: number
          email: string
          fullName: string | null
          password: string
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: number
          email: string
          fullName?: string | null
          password: string
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: number
          email?: string
          fullName?: string | null
          password?: string
          createdAt?: string
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            isOneToOne: false
            referencedRelation: "access_tokens"
            referencedColumns: ["userId"]
          },
          {
            foreignKeyName: "messages_userId_fkey"
            columns: ["id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["userId"]
          }
        ]
      },
      access_tokens: {
        Row: {
          id: number
          userId: number
          name: string | null
          type: string
          hash: string
          expiresAt: string | null
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: number
          userId: number
          name?: string | null
          type: string
          hash: string
          expiresAt?: string | null
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: number
          userId?: number
          name?: string | null
          type?: string
          hash?: string
          expiresAt?: string | null
          createdAt?: string
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "access_tokens_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      messages: {
        Row: {
          id: number
          senderName: string
          senderNumber: string
          receiverName: string
          receiverNumber: string
          textMessage: string
          projectName: string
          date: string
          createdAt: string
          updatedAt: string
          userId: number | null
        }
        Insert: {
          id?: number
          senderName: string
          senderNumber: string
          receiverName: string
          receiverNumber: string
          textMessage: string
          projectName: string
          date: string
          createdAt?: string
          updatedAt?: string
          userId?: number | null
        }
        Update: {
          id?: number
          senderName?: string
          senderNumber?: string
          receiverName?: string
          receiverNumber?: string
          textMessage?: string
          projectName?: string
          date?: string
          createdAt?: string
          updatedAt?: string
          userId?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    },
    Views: {
      [_ in never]: never
    },
    Functions: {
      [_ in never]: never
    },
    Enums: {
      [_ in never]: never
    },
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
