/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from './kernel.js'

router.get('/', async () => {
  return {
    hello: 'world',
  }
})

// Auth routes (public)
router.group(() => {
  router.post('/login', '#controllers/auth_controller.login')
  router.post('/register', '#controllers/auth_controller.register')
}).prefix('/api/auth')

// Import direct routes
import { directLogin, directMe } from './direct_routes.js'

// Direct routes
router.post('/api/direct-login', directLogin)
router.get('/api/direct-me', directMe)

// Auth routes (protected)
router.group(() => {
  router.get('/me', '#controllers/auth_controller.me')
  router.post('/logout', '#controllers/auth_controller.logout')
})
  .prefix('/api/auth')

// User routes (protected)
router.group(() => {
  router.get('/', '#controllers/users_controller.index')
  router.post('/', '#controllers/users_controller.store')
  router.get('/:id', '#controllers/users_controller.show')
  router.put('/:id', '#controllers/users_controller.update')
  router.delete('/:id', '#controllers/users_controller.destroy')
})
  .prefix('/api/users')

// Message routes (protected)
router.group(() => {
  router.get('/', '#controllers/messages_controller.index')
  router.post('/', '#controllers/messages_controller.store')
  router.get('/:id', '#controllers/messages_controller.show')
  router.put('/:id', '#controllers/messages_controller.update')
  router.delete('/:id', '#controllers/messages_controller.destroy')
})
  .prefix('/api/messages')

// QR code routes (protected)
router.group(() => {
  router.get('/', '#controllers/qrcode_controller.index')
  router.post('/', '#controllers/qrcode_controller.store')
  router.post('/:id/generate', '#controllers/qrcode_controller.generate')
  router.post('/:id/activate', '#controllers/qrcode_controller.activate')
  router.post('/:id/deactivate', '#controllers/qrcode_controller.deactivate')
  router.delete('/:id', '#controllers/qrcode_controller.destroy')
})
  .prefix('/api/whatsapp')
