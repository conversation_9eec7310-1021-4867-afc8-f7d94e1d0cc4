/*
|--------------------------------------------------------------------------
| Direct Routes file
|--------------------------------------------------------------------------
|
| This file contains routes that don't use the auth middleware
|
*/

import type { HttpContext } from '@adonisjs/core/http'

// Direct login handler
export async function directLogin({ request, response }: HttpContext) {
  const { email, password } = request.only(['email', 'password'])

  try {
    // Find user by email
    const User = (await import('#models/user')).default
    const user = await User.findBy('email', email)

    if (!user) {
      return response.unauthorized({ error: 'Invalid credentials' })
    }

    // Verify password
    const hash = (await import('@adonisjs/core/services/hash')).default
    const isPasswordValid = await hash.verify(user.password, password)

    if (!isPasswordValid) {
      return response.unauthorized({ error: 'Invalid credentials' })
    }

    // Generate a simple token
    const token = `token_${Date.now()}_${Math.random().toString(36).substring(2)}`

    // Calculate expiry date (7 days from now)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)

    return response.json({
      token,
      type: 'bearer',
      expires_at: expiresAt.toISOString(),
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
      }
    })
  } catch (error) {
    console.error('Login error:', error)
    return response.internalServerError({
      error: 'An error occurred during login',
      message: error.message
    })
  }
}

// Direct me handler
export function directMe({ response }: HttpContext) {
  return response.json({
    id: 1,
    email: '<EMAIL>',
    fullName: 'Test User',
  })
}
