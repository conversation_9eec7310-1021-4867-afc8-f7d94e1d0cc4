import { createClient } from '@libsql/client'
import { createHash, randomBytes } from 'crypto'

// Simple password hashing function for testing
function hashPassword(password: string): string {
  const salt = randomBytes(16).toString('hex')
  const hash = createHash('sha256')
    .update(salt + password)
    .digest('hex')
  return `${salt}:${hash}`
}

async function main() {
  try {
    // Replace with your actual Turso credentials
    const client = createClient({
      url: process.env.TURSO_DATABASE_URL || 'libsql://your-database-name.turso.io',
      authToken: process.env.TURSO_AUTH_TOKEN || 'your-auth-token',
    })

    // Create users table if it doesn't exist
    await client.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        full_name TEXT,
        password TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `)

    // Create access_tokens table if it doesn't exist
    await client.execute(`
      CREATE TABLE IF NOT EXISTS access_tokens (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        name TEXT,
        hash TEXT UNIQUE NOT NULL,
        expires_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // Create messages table if it doesn't exist
    await client.execute(`
      CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sender_name TEXT NOT NULL,
        sender_number TEXT NOT NULL,
        receiver_name TEXT NOT NULL,
        receiver_number TEXT NOT NULL,
        text_message TEXT NOT NULL,
        project_name TEXT NOT NULL,
        date TEXT NOT NULL,
        user_id INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `)

    // Create indexes
    await client.execute(`CREATE INDEX IF NOT EXISTS idx_access_tokens_user_id ON access_tokens(user_id)`)
    await client.execute(`CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id)`)
    await client.execute(`CREATE INDEX IF NOT EXISTS idx_messages_sender_number ON messages(sender_number)`)
    await client.execute(`CREATE INDEX IF NOT EXISTS idx_messages_receiver_number ON messages(receiver_number)`)
    await client.execute(`CREATE INDEX IF NOT EXISTS idx_messages_project_name ON messages(project_name)`)
    await client.execute(`CREATE INDEX IF NOT EXISTS idx_messages_date ON messages(date)`)

    // Create a test user
    const hashedPassword = hashPassword('password123')
    const now = new Date().toISOString()
    
    const userResult = await client.execute({
      sql: `
        INSERT INTO users (email, full_name, password, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `,
      args: ['<EMAIL>', 'Turso Test User', hashedPassword, now, now]
    })
    
    const userId = userResult.lastInsertId
    console.log('Test user created with ID:', userId)

    // Create a test token
    const tokenHash = 'test_token_' + Math.random().toString(36).substring(2)
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    
    const tokenResult = await client.execute({
      sql: `
        INSERT INTO access_tokens (user_id, type, name, hash, expires_at, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `,
      args: [userId, 'api', 'Test Token', tokenHash, expiresAt, now, now]
    })
    
    console.log('Test token created with ID:', tokenResult.lastInsertId)

    // Create a test message
    const messageResult = await client.execute({
      sql: `
        INSERT INTO messages (
          sender_name, sender_number, receiver_name, receiver_number,
          text_message, project_name, date, user_id, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
      args: [
        'John Doe', '+1234567890', 'Jane Smith', '+0987654321',
        'Hello, this is a test message!', 'Test Project', now, userId, now, now
      ]
    })
    
    console.log('Test message created with ID:', messageResult.lastInsertId)

    // Query the user with token
    const userWithToken = await client.execute({
      sql: `
        SELECT u.*, t.hash as token_hash, t.expires_at
        FROM users u
        JOIN access_tokens t ON u.id = t.user_id
        WHERE u.id = ?
      `,
      args: [userId]
    })
    
    console.log('User with token:', userWithToken.rows[0])

    console.log('\nTurso database setup completed successfully!')
    console.log('To test the API endpoints with Turso:')
    console.log('1. Update your .env file with the actual Turso credentials')
    console.log('2. Start the server with: node ace serve --watch')
    console.log('3. Register a new user:')
    console.log('   curl -X POST http://localhost:3333/api/auth/register -H "Content-Type: application/json" -d \'{"email":"<EMAIL>","fullName":"Test User","password":"password123"}\'')
    console.log('4. Login with the user:')
    console.log('   curl -X POST http://localhost:3333/api/auth/login -H "Content-Type: application/json" -d \'{"email":"<EMAIL>","password":"password123"}\'')
    console.log('5. Use the token to access protected routes:')
    console.log('   curl -X GET http://localhost:3333/api/auth/me -H "Authorization: Bearer YOUR_TOKEN"')
  } catch (error) {
    console.error('Error:', error)
  }
}

main()
