import { BaseSeeder } from '@adonisjs/lucid/seeders'
import WhatsappNumber from '#models/whatsapp_number'

export default class extends BaseSeeder {
  async run() {
    // Create some sample WhatsApp numbers
    await WhatsappNumber.createMany([
      {
        phoneNumber: '5511999999999',
        contactName: '<PERSON>',
        projectName: 'Projeto A',
        status: 'active',
        userId: 1,
      },
      {
        phoneNumber: '5511888888888',
        contactName: '<PERSON>',
        projectName: 'Projeto B',
        status: 'pending',
        userId: 1,
      },
      {
        phoneNumber: '5511777777777',
        contactName: '<PERSON>',
        projectName: 'Projeto C',
        status: 'inactive',
        userId: 1,
      },
    ])
  }
}