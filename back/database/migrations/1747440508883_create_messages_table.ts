import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'messages'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('sender_name').notNullable()
      table.string('sender_number').notNullable()
      table.string('receiver_name').notNullable()
      table.string('receiver_number').notNullable()
      table.text('text_message').notNullable()
      table.string('project_name').notNullable()
      table.timestamp('date').notNullable()

      table.integer('user_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('SET NULL')
        .nullable()

      // Create indexes
      table.index(['user_id'])
      table.index(['sender_number'])
      table.index(['receiver_number'])
      table.index(['project_name'])
      table.index(['date'])

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
