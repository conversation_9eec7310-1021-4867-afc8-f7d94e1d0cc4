import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'messages'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add media_url column for storing media file paths
      table.string('media_url').nullable()
      
      // Add media_type column for storing media type (image, video, audio, etc.)
      table.string('media_type').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('media_url')
      table.dropColumn('media_type')
    })
  }
}
