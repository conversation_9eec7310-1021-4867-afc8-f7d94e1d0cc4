import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'whatsapp_numbers'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add session_data column for storing WhatsApp session information
      table.text('session_data').nullable()
      
      // Add last_connected column to track when the WhatsApp number was last connected
      table.timestamp('last_connected', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('session_data')
      table.dropColumn('last_connected')
    })
  }
}
