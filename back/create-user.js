import { PrismaClient } from './generated/prisma/index.js';
const prisma = new PrismaClient();

async function main() {
  try {
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        fullName: 'Test User',
        password: '$scrypt$n=16384,r=8,p=1$c2FsdHlzYWx0eXNhbHR5$Tm9XIHRoYXQgeW91IGRlY29kZWQgdGhpcywgaGF2ZSBhIGNvb2tpZSE='
      }
    });
    console.log('User created:', user);
  } catch (e) {
    console.error(e);
  } finally {
    await prisma.$disconnect();
  }
}

main();
