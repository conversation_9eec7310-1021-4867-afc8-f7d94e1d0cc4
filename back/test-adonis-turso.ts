import { createClient } from '@libsql/client'
import env from '#start/env'

async function main() {
  try {
    // Create a Turso client
    const client = createClient({
      url: env.get('TURSO_DATABASE_URL'),
      authToken: env.get('TURSO_AUTH_TOKEN'),
    })

    // Test the connection
    const result = await client.execute('SELECT 1 as test')
    console.log('Connection test result:', result.rows[0])

    console.log('\nTurso connection successful!')
    console.log('To run AdonisJS migrations:')
    console.log('1. Run: node ace migration:run')
    console.log('2. Start the server: node ace serve --watch')
    console.log('3. Test the API endpoints:')
    console.log('   - Register: POST /api/auth/register')
    console.log('   - Login: POST /api/auth/login')
    console.log('   - Get user: GET /api/auth/me')
    console.log('   - Create message: POST /api/messages')
    console.log('   - List messages: GET /api/messages')
  } catch (error) {
    console.error('Error connecting to <PERSON>rs<PERSON>:', error)
  }
}

main()
