import env from '#start/env'
import { defineConfig } from '@adonisjs/lucid'

const dbConfig = defineConfig({
  connection: 'turso',
  connections: {
    sqlite: {
      client: 'sqlite3',
      connection: {
        filename: env.get('SQLITE_FILENAME', 'tmp/db.sqlite3'),
      },
      useNullAsDefault: true,
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
    },
    turso: {
      client: 'libsql',
      connection: {
        filename: `${env.get('TURSO_DATABASE_URL')}?authToken=${env.get('TURSO_AUTH_TOKEN')}`,
      },
      useNullAsDefault: true,
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
    },
  },
})

export default dbConfig