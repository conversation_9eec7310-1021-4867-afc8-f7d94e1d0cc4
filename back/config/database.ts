import env from '#start/env'
import { defineConfig } from '@adonisjs/lucid'

const dbConfig = defineConfig({
  connection: 'turso',
  connections: {
    sqlite: {
      client: 'sqlite3',
      connection: {
        filename: env.get('SQLITE_FILENAME', 'tmp/db.sqlite3'),
      },
      useNullAsDefault: true,
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
    },
    turso: {
      client: 'better-sqlite3',
      connection: {
        filename: env.get('SQLITE_FILENAME', 'tmp/db.sqlite3'),
      },
      useNullAsDefault: true,
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
      // This is where we'll add Turso-specific configuration
      pool: {
        afterCreate: (conn, cb) => {
          // This is where we would connect to Turso if we were using a direct connection
          // For now, we'll use the SQLite file as a fallback
          cb(null, conn)
        },
      },
    },
  },
})

export default dbConfig