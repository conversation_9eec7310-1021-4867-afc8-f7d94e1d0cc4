/**
 * Simple script to check WhatsApp number data in database
 */

async function checkDatabase() {
  try {
    // Import the WhatsApp number model
    const { default: WhatsappNumber } = await import('./app/models/whatsapp_number.js');

    // Find the WhatsApp number
    const number = await WhatsappNumber.find(11);

    if (number) {
      console.log('=== WhatsApp Number Data ===');
      console.log('ID:', number.id);
      console.log('Phone Number:', number.phoneNumber);
      console.log('Contact Name:', number.contactName);
      console.log('Project Name:', number.projectName);
      console.log('Status:', number.status);
      console.log('QR Code ID:', number.qrCodeId || 'NULL');
      console.log('Session Data:', number.sessionData || 'NULL');
      console.log('Last Connected:', number.lastConnected?.toISO() || 'NULL');
      console.log('Created At:', number.createdAt?.toISO() || 'NULL');
      console.log('Updated At:', number.updatedAt?.toISO() || 'NULL');
    } else {
      console.log('WhatsApp number with ID 11 not found');
    }

    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkDatabase();
