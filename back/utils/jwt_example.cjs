/**
 * Example script to generate JWT tokens for WhatsApp API
 *
 * Usage:
 * node utils/jwt_example.cjs
 */

const jwt = require('jsonwebtoken')

// This should match the WHATSAPP_JWT_SECRET in your .env file
const SECRET = 'B4DC9A8F6F45E5E66BD68D62CC8B6A12'

// Example payload
const payload = {
  message: 'Hello from WhatsApp API! This is a test message.',
  client_number: '5511999999999', // Brazilian phone number
  project_name: 'Novo Projeto', // Use the actual project name from database
  secret: SECRET
}

// Generate JWT token
const token = jwt.sign(payload, SECRET, { expiresIn: '1h' })

console.log('Generated JWT Token:')
console.log(token)
console.log('\n')

console.log('Example cURL request to send message:')
console.log(`curl -X POST http://localhost:3333/api/whatsapp-api/send \\
  -H "Content-Type: application/json" \\
  -d '{"token": "${token}"}'`)
console.log('\n')

console.log('Example cURL request to get status:')
console.log(`curl -X GET "http://localhost:3333/api/whatsapp-api/status?token=${token}"`)
console.log('\n')

console.log('Decoded payload:')
console.log(jwt.decode(token))
