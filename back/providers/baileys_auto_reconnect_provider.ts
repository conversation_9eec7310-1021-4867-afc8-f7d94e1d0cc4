import type { ApplicationService } from '@adonisjs/core/types'

export default class BaileysAutoReconnectProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {}

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {
    // Wait a bit for the database to be ready
    setTimeout(async () => {
      try {
        // Import the Baileys service
        const baileysService = (await import('#services/baileys_service')).default
        
        // Start auto-reconnection process
        await baileysService.autoReconnectWhatsAppNumbers()
      } catch (error) {
        console.error('Error during auto-reconnection startup:', error)
      }
    }, 5000) // Wait 5 seconds after server is ready
  }

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
