import { ApplicationService } from '@adonisjs/core/types'
import { HttpServer } from '@adonisjs/core/http'
import { useInitializeWebSockets, useAutoReconnectWhatsAppNumbers } from '#services/baileys/hooks'
import logger from '#services/baileys/logger'

/**
 * Baileys provider for AdonisJS
 */
export default class BaileysProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register the Baileys service
   */
  register() {
    // The service is already registered as a singleton in its own module
  }

  /**
   * Boot the Baileys service
   */
  async boot() {
    // Initialize WebSockets when the HTTP server is ready
    this.app.container.resolveBinding('http.server').then((server: HttpServer) => {
      useInitializeWebSockets(server.instance)
      logger.info('Baileys WebSocket server initialized')
    })
  }

  /**
   * Ready handler
   */
  async ready() {
    // Import the Baileys service to initialize it
    await import('#services/baileys/index')
    logger.info('Baileys service ready')

    // Auto-reconnect WhatsApp numbers
    setTimeout(async () => {
      try {
        await useAutoReconnectWhatsAppNumbers()
      } catch (error) {
        logger.error('Error auto-reconnecting WhatsApp numbers:', error)
      }
    }, 5000) // Wait 5 seconds to ensure database is ready
  }

  /**
   * Shutdown handler
   */
  async shutdown() {
    logger.info('Shutting down Baileys service')
  }
}
