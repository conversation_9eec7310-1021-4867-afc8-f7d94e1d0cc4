import { createClient } from '@libsql/client'
import type { ApplicationService } from '@adonisjs/core/types'
import env from '#start/env'

/**
 * Turso service provider to integrate Turso with AdonisJS
 */
export default class TursoProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register the Turso client as a singleton
   */
  register() {
    this.app.container.singleton('turso', () => {
      const client = createClient({
        url: env.get('TURSO_DATABASE_URL'),
        authToken: env.get('TURSO_AUTH_TOKEN'),
      })
      return client
    })
  }

  /**
   * Cleanup resources when the application is shutting down
   */
  async shutdown() {
    // No cleanup needed for Turso client
  }
}
