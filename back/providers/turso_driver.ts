import { createClient } from '@libsql/client'
import type { ApplicationService } from '@adonisjs/core/types'
import env from '#start/env'

/**
 * Turso driver for AdonisJS
 */
export default class TursoDriver {
  #app: ApplicationService
  #client: any

  constructor(app: ApplicationService) {
    this.#app = app
  }

  /**
   * Register the Turso driver
   */
  register() {
    // Create the Turso client
    this.#client = createClient({
      url: env.get('TURSO_DATABASE_URL'),
      authToken: env.get('TURSO_AUTH_TOKEN'),
    })
  }

  /**
   * Boot the Turso driver
   */
  async boot() {
    // Nothing to do here
  }

  /**
   * Ready the Turso driver
   */
  async ready() {
    // Nothing to do here
  }

  /**
   * Shutdown the Turso driver
   */
  async shutdown() {
    // Close the Turso client connection
    if (this.#client && typeof this.#client.close === 'function') {
      await this.#client.close()
    }
  }
}
